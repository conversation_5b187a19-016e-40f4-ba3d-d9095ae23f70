name: CI

on:
  push:
    branches: [ main, dev ]
  pull_request:
    branches: [ main, dev ]

permissions:
  pull-requests: write
  contents: read

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v4
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '22'
        cache: 'npm'
        cache-dependency-path: webapp/package-lock.json
    
    - name: Install dependencies
      working-directory: webapp
      run: npm ci
    
    - name: Run linter
      working-directory: webapp
      run: npm run lint
    
    - name: Run tests with coverage
      working-directory: webapp
      run: npm run test:coverage
    
    - name: TypeScript Code Coverage Report
      uses: irongut/CodeCoverageSummary@v1.3.0
      with:
        filename: webapp/coverage/cobertura-coverage.xml
        badge: true
        format: markdown
        hide_branch_rate: false
        hide_complexity: true
        indicators: true
        output: both
        thresholds: "75 85"
    
    - name: Add Coverage PR Comment
      uses: marocchino/sticky-pull-request-comment@v2
      if: github.event_name == 'pull_request'
      with:
        header: "TypeScript Code Coverage"
        message: "TypeScript code coverage report:"
        recreate: true
        path: code-coverage-results.md

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Build Docker image
      working-directory: webapp
      run: |
        docker build -t wefi-b2b-dashboard:${GITHUB_SHA} .
        docker save wefi-b2b-dashboard:${GITHUB_SHA} > wefi-b2b-dashboard.tar 
