services:
  app:
    build:
      context: ./webapp
      dockerfile: Dockerfile
    ports:
      - 80:80

  postgres:
    image: postgres:16
    environment:
      POSTGRES_PASSWORD: postgres
    ports:
      - 5432:5432
    volumes:
      - postgres_data:/var/lib/postgresql/data

  pgadmin:
    image: dpage/pgadmin4
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: pgadmin
    ports:
      - 5050:80
    volumes:
      - pgadmin_data:/pgadmin

  redis:
    image: redis:latest
    ports:
      - 6379:6379
    volumes:
      - redis_data:/data

volumes:
  postgres_data:
  redis_data:
  pgadmin_data: