# WeFi B2B Dashboard (WebApp)

This is React TypeScript application for WeFi B2B Dashboard.

## Getting Started

### Prerequisites

- Node.js (v22 or higher)
  - Recommended: https://github.com/nvm-sh/nvm
- npm, pnpm, yarn or bun
- Docker (optional, for containerized development)

### Installation

1. Clone the repository:
```bash
git clone https://github.com/wefico/wefi-b2b-dashboard.git
cd wefi-b2b-dashboard/webapp
```

2. Install dependencies:
```bash
npm install
# or
yarn install
```

3. Start the development server:
```bash
npm run dev
# or
yarn dev
```

### Available Commands

- `npm run dev` - Start development server
- `npm run build` - Create production build
- `npm run preview` - Preview production build locally
- `npm run test` - Run tests
- `npm run test:coverage` - Run tests with coverage report
- `npm run format` - Format code with Biome
- `npm run lint` - Lint code with Biome
- `npm run check` - Check and fix code with Biome

### Docker Setup

To run the application using Docker:

```bash
# Build the image
docker build -t wefi-b2b-dashboard .

# Run the container
docker run -p 3000:80 wefi-b2b-dashboard

To avoid being stuck in Docker for all of etenity, you can use CTRL + C to exit the docker process.

# Run the container in background
docker run -d -p 3000:80 wefi-b2b-dashboard
```

## Project Structure

```
wefi-b2b-dashboard/
└── webapp/                    # Frontend web application
    ├── src/
    │   ├── assets/           # Imported assets (images, fonts, etc.)
    │   ├── components/        # Shared/common UI components
    │   ├── features/          # Feature-based modules
    │   │   └── [feature]/    # Each feature follows the same structure:
    │   │       ├── components/  # Feature-specific components
    │   │       ├── hooks/       # Feature-specific hooks
    │   │       └── types/       # Feature-specific types
    │   ├── routes/           # Route components and layouts
    │   ├── hooks/            # Global/shared hooks
    │   ├── lib/              # Library code
    │   │   ├── types/        # Global TypeScript type definitions
    │   │   └── utils/        # Utility functions
    │   ├── store/            # State management
    │   ├── styles/           # Global styles and theme
    │   ├── test/             # Test utilities and setup
    │   ├── App.tsx           # Application component
    │   ├── main.tsx          # Entry point
    │   └── routeTree.gen.ts  # Generated route tree
    ├── .github/              # GitHub Actions workflows
    ├── dist/                 # Build output directory
    ├── coverage/             # Test coverage reports
    ├── public/               # Public static files (served as-is)
    ├── .dockerignore         # Docker ignore rules
    ├── .env.example          # Example environment variables
    ├── .gitignore           # Git ignore rules
    ├── biome.json           # Biome configuration
    ├── components.json      # UI components configuration
    ├── Dockerfile           # Docker build configuration
    ├── index.html           # HTML entry point
    ├── nginx.conf           # Nginx configuration for production
    ├── package.json         # Project dependencies and scripts
    ├── package-lock.json    # Locked dependencies
    ├── tsconfig.json        # TypeScript configuration
    ├── tsconfig.node.json   # TypeScript Node configuration
    ├── vite.config.ts       # Vite configuration
    └── vitest.config.ts     # Vitest test configuration
```

## Assets Handling

The project handles assets in two ways:

### 1. Imported Assets (`src/assets/`)
- Used for assets that need to be processed/optimized by Vite
- Import these assets directly in your components
- Examples:
  ```tsx
  import logo from '@/assets/images/logo.svg'
  import '@/assets/fonts/inter.css'
  ```
- Benefits:
  - Assets are processed and optimized by Vite
  - Automatic hash-based cache busting
  - TypeScript support for imports
  - Dead code elimination

### 2. Public Assets (`public/`)
- Used for:
  - Files that need to be served as-is
  - Large files that don't need processing
  - Files that need a direct public URL
- Access via absolute URLs from your code
- Examples:
  ```tsx
  <img src="/images/large-photo.jpg" />
  <link rel="icon" href="/favicon.ico" />
  ```
- Benefits:
  - Direct file serving without processing
  - Predictable URLs
  - Better for large files that don't need optimization

## Code Quality

The project uses Biome for code quality:

```bash
# Format code
npm run format

# Lint code
npm run lint

# Check and fix code
npm run check
```

## Testing

Run the test suite:

```bash
# Run tests
npm run test

# Run tests with coverage
npm run test:coverage
```

## CI/CD

The project includes GitHub Actions workflows for:
- Linting
- Unit Testing
- Build verification

## Code Coverage

The project maintains code coverage thresholds to ensure code quality:

- Local tests will pass with coverage of 75% or higher
- In Pull Requests, coverage is indicated by:
  - 🟡 Yellow: 75% to 85% coverage
  - 🟢 Green: 85% and above coverage
  - 🔴 Red: Below 75% coverage

Note: These thresholds are temporary and will be increased in the future as the codebase matures.

## 3rd party packages

#### TanStack

The application heavily relies on the TanStack ecosystem of libraries:

- TanStack Query: Powerful data fetching, caching and state management
- TanStack Router: Type-safe routing with automatic code splitting
- TanStack Forms: Flexible form state management and validation

These libraries provide robust, production-ready solutions for common application needs while reducing boilerplate code and development time. Each library is designed to work seamlessly together while remaining independently useful.

#### Zod

Zod is a TypeScript-first schema validation library used in this project for runtime type checking and data validation. It works seamlessly with our form handling to ensure data integrity and type safety throughout the application.

#### Shadcn

Shadcn provides a collection of reusable, accessible and customizable React components built with Radix UI and styled with Tailwind CSS. Unlike traditional component libraries, Shadcn components are copied directly into your project, giving you full control over the code and styling. The components follow best practices for accessibility and are fully customizable to match your application's design system. Link: https://ui.shadcn.com/.

Key benefits:
- Full source code access and control
- Built on Radix UI primitives for robust accessibility
- Styled with Tailwind CSS for easy customization
- Copy-paste installation rather than package dependencies
- Regular updates and active community
