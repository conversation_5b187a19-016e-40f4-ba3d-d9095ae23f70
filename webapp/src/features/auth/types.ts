export interface SignUpFormData {
  email: string;
  password: string;
}

export interface SignInFormData {
  email: string;
  password: string;
}

export interface OTPFormData {
  code: string;
}

export interface BusinessDetailsFormData {
  firstName: string;
  lastName: string;
  addressLine1: string;
  addressLine2?: string;
  state: string;
  city: string;
  zipCode: string;
}

export interface JWTTokens {
  accessToken: string;
  refreshToken: string;
}

export interface DecodedJWTPayload {
  sub: string;
  email: string;
  exp: number;
  iat: number;
}

export interface User {
  id: string;
  email: string;
  firstName?: string;
  lastName?: string;
  isEmailVerified: boolean;
  businessDetails?: BusinessDetailsFormData;
}

export interface AuthApiResponse<T = unknown> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

export interface SignUpResponse {
  message: string;
  email: string;
}

export interface SignInResponse {
  user: User;
  tokens: JWTTokens;
}

export interface OTPVerificationResponse {
  message: string;
  isVerified: boolean;
}

export interface BusinessDetailsResponse {
  user: User;
  message: string;
}

export type AuthStep =
  | 'sign-in'
  | 'sign-up'
  | 'otp-verification'
  | 'email-confirmed'
  | 'wallet-creation'
  | 'wallet-created'
  | 'completed';

export interface AuthState {
  user: User | null;
  tokens: JWTTokens | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  currentStep: AuthStep;
  signUpEmail: string | null;
}

export interface ApiError {
  message: string;
  code?: string;
  field?: string;
}

export interface ValidationError {
  field: string;
  message: string;
}

export interface PasswordRequirements {
  minLength: number;
  requireUppercase: boolean;
  requireLowercase: boolean;
  requireNumbers: boolean;
  requireSpecialChars: boolean;
}

export interface OTPConfig {
  length: number;
  expiryMinutes: number;
  resendCooldownSeconds: number;
}

export interface AuthConfig {
  passwordRequirements: PasswordRequirements;
  otpConfig: OTPConfig;
  tokenExpiryHours: number;
  refreshTokenExpiryDays: number;
}
