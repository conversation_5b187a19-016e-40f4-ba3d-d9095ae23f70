import { describe, it, expect, beforeEach, vi } from 'vitest';
import {
  signUpApi,
  signInApi,
  verifyOT<PERSON>pi,
  resendOT<PERSON>pi,
  submitBusinessDetailsApi,
  refreshTokenApi,
  logoutApi,
  clearMockData,
  getMockUserByEmail,
} from './mockAuthApi';

vi.spyOn(console, 'log').mockImplementation(() => {});

describe('Mock Auth API', () => {
  beforeEach(() => {
    clearMockData();
  });

  describe('signUpApi', () => {
    it('should successfully sign up with valid data', async () => {
      const signUpData = {
        email: '<EMAIL>',
        password: 'Password123!',
      };

      const result = await signUpApi(signUpData);

      expect(result.success).toBe(true);
      expect(result.data?.email).toBe(signUpData.email);
      expect(result.data?.message).toBe('Confirmation code sent to your email');
    });

    it('should reject invalid email format', async () => {
      const signUpData = {
        email: 'invalid-email',
        password: 'Password123!',
      };

      const result = await signUp<PERSON>pi(signUpData);

      expect(result.success).toBe(false);
      expect(result.error).toBe('Please enter a valid email address');
    });

    it('should reject short password', async () => {
      const signUpData = {
        email: '<EMAIL>',
        password: '123',
      };

      const result = await signUpApi(signUpData);

      expect(result.success).toBe(false);
      expect(result.error).toBe('Password must be at least 8 characters long');
    });

    it('should reject duplicate email', async () => {
      const signUpData = {
        email: '<EMAIL>',
        password: 'Password123!',
      };

      await signUpApi(signUpData);
      await verifyOTPApi(signUpData.email, { code: '123456' }); // Mock OTP
      await submitBusinessDetailsApi(signUpData.email, {
        firstName: 'John',
        lastName: 'Doe',
        addressLine1: '123 Main St',
        state: 'CA',
        city: 'San Francisco',
        zipCode: '94102',
      });

      const result = await signUpApi(signUpData);

      expect(result.success).toBe(false);
      expect(result.error).toBe('User with this email already exists');
    });
  });

  describe('signInApi', () => {
    it('should successfully sign in with valid credentials', async () => {
      const email = '<EMAIL>';
      await signUpApi({ email, password: 'Password123!' });
      await verifyOTPApi(email, { code: '123456' });
      await submitBusinessDetailsApi(email, {
        firstName: 'John',
        lastName: 'Doe',
        addressLine1: '123 Main St',
        state: 'CA',
        city: 'San Francisco',
        zipCode: '94102',
      });

      const result = await signInApi({
        email,
        password: 'password123',
      });

      expect(result.success).toBe(true);
      expect(result.data?.user.email).toBe(email);
      expect(result.data?.tokens).toBeDefined();
      expect(result.data?.tokens.accessToken).toContain('mock.access.token');
    });

    it('should reject invalid email', async () => {
      const result = await signInApi({
        email: '<EMAIL>',
        password: 'password123',
      });

      expect(result.success).toBe(false);
      expect(result.error).toBe('Invalid email or password');
    });

    it('should reject unverified email', async () => {
      const email = '<EMAIL>';
      await signUpApi({ email, password: 'Password123!' });

      const result = await signInApi({
        email,
        password: 'password123',
      });

      expect(result.success).toBe(false);
      expect(result.error).toBe('Please verify your email before signing in');
    });
  });

  describe('verifyOTPApi', () => {
    it('should successfully verify correct OTP', async () => {
      const email = '<EMAIL>';
      await signUpApi({ email, password: 'Password123!' });

      const result = await verifyOTPApi(email, { code: '123456' });

      expect(result.success).toBe(true);
      expect(result.data?.isVerified).toBe(true);
    });

    it('should reject incorrect OTP', async () => {
      const email = '<EMAIL>';
      await signUpApi({ email, password: 'Password123!' });

      const result = await verifyOTPApi(email, { code: '000000' });

      expect(result.success).toBe(false);
      expect(result.error).toBe('Invalid OTP code');
    });

    it('should reject OTP for non-existent email', async () => {
      const result = await verifyOTPApi('<EMAIL>', { code: '123456' });

      expect(result.success).toBe(false);
      expect(result.error).toBe('OTP not found or expired');
    });
  });

  describe('resendOTPApi', () => {
    it('should successfully resend OTP', async () => {
      const email = '<EMAIL>';

      const result = await resendOTPApi(email);

      expect(result.success).toBe(true);
      expect(result.data?.message).toBe('New confirmation code sent to your email');
    });
  });

  describe('submitBusinessDetailsApi', () => {
    it('should successfully submit business details', async () => {
      const email = '<EMAIL>';
      const businessDetails = {
        firstName: 'John',
        lastName: 'Doe',
        addressLine1: '123 Main St',
        state: 'CA',
        city: 'San Francisco',
        zipCode: '94102',
      };

      const result = await submitBusinessDetailsApi(email, businessDetails);

      expect(result.success).toBe(true);
      expect(result.data?.user.email).toBe(email);
      expect(result.data?.user.firstName).toBe(businessDetails.firstName);
      expect(result.data?.user.isEmailVerified).toBe(true);
    });
  });

  describe('refreshTokenApi', () => {
    it('should successfully refresh valid token', async () => {
      const refreshToken = 'mock.refresh.token.user123.1234567890';

      const result = await refreshTokenApi(refreshToken);

      expect(result.success).toBe(true);
      expect(result.data?.accessToken).toContain('mock.access.token');
      expect(result.data?.refreshToken).toContain('mock.refresh.token');
    });

    it('should reject invalid refresh token', async () => {
      const result = await refreshTokenApi('invalid-token');

      expect(result.success).toBe(false);
      expect(result.error).toBe('Invalid refresh token');
    });
  });

  describe('logoutApi', () => {
    it('should successfully logout', async () => {
      const result = await logoutApi();

      expect(result.success).toBe(true);
      expect(result.data?.message).toBe('Logged out successfully');
    });
  });

  describe('Helper functions', () => {
    it('should get user by email', async () => {
      const email = '<EMAIL>';
      await signUpApi({ email, password: 'Password123!' });
      await verifyOTPApi(email, { code: '123456' });
      await submitBusinessDetailsApi(email, {
        firstName: 'John',
        lastName: 'Doe',
        addressLine1: '123 Main St',
        state: 'CA',
        city: 'San Francisco',
        zipCode: '94102',
      });

      const user = getMockUserByEmail(email);

      expect(user).toBeDefined();
      expect(user?.email).toBe(email);
    });

    it('should return undefined for non-existent user', () => {
      const user = getMockUserByEmail('<EMAIL>');

      expect(user).toBeUndefined();
    });
  });
});
