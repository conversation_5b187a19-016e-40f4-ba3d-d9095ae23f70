import {
  SignUpFormData,
  SignInFormData,
  OTPFormData,
  BusinessDetailsFormData,
  AuthApiResponse,
  SignUpResponse,
  SignInResponse,
  OTPVerificationResponse,
  BusinessDetailsResponse,
  JWTTokens,
  User,
} from '../types';

// Mock delay to simulate network requests
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

// Mock JWT token generation
export const generateMockTokens = (userId: string, email: string): JWTTokens => {
  const now = Math.floor(Date.now() / 1000);
  const accessTokenPayload = {
    sub: userId,
    email,
    exp: now + 3600, // 1 hour
    iat: now,
  };
  
  // In real implementation, these would be properly signed JWT tokens
  const accessToken = `mock.access.token.${btoa(JSON.stringify(accessTokenPayload))}`;
  const refreshToken = `mock.refresh.token.${userId}.${now}`;
  
  return { accessToken, refreshToken };
};

// Mock user storage (in real app, this would be a database)
const mockUsers: Map<string, User> = new Map();
const mockOTPCodes: Map<string, { code: string; expires: number }> = new Map();
const mockPasswords: Map<string, string> = new Map(); // Store passwords by email

// Create a demo user for testing
const demoUser: User = {
  id: 'demo_user_123',
  email: '<EMAIL>',
  firstName: 'Demo',
  lastName: 'User',
  isEmailVerified: true,
  businessDetails: {
    firstName: 'Demo',
    lastName: 'User',
    addressLine1: '123 Demo Street',
    addressLine2: 'Suite 100',
    state: 'California',
    city: 'San Francisco',
    zipCode: '94102',
  },
};

// Add demo user to storage
mockUsers.set(demoUser.id, demoUser);
mockPasswords.set(demoUser.email, 'demo123');

// Mock sign-up API
export const signUpApi = async (data: SignUpFormData): Promise<AuthApiResponse<SignUpResponse>> => {
  await delay(1000);
  
  // Validate email format
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(data.email)) {
    return {
      success: false,
      error: 'Please enter a valid email address',
    };
  }
  
  // Validate password
  if (data.password.length < 8) {
    return {
      success: false,
      error: 'Password must be at least 8 characters long',
    };
  }
  
  // Check if user already exists
  const existingUser = Array.from(mockUsers.values()).find(user => user.email === data.email);
  if (existingUser) {
    return {
      success: false,
      error: 'User with this email already exists',
    };
  }
  
  // Store password for later sign-in
  mockPasswords.set(data.email, data.password);

  // Generate OTP (use fixed code for development and testing)
  const otpCode = process.env.NODE_ENV === 'production' ? Math.floor(100000 + Math.random() * 900000).toString() : '123456';
  const expires = Date.now() + 10 * 60 * 1000; // 10 minutes
  mockOTPCodes.set(data.email, { code: otpCode, expires });

  console.log(`Mock OTP for ${data.email}: ${otpCode}`); // For testing purposes
  
  return {
    success: true,
    data: {
      message: 'Confirmation code sent to your email',
      email: data.email,
    },
  };
};

// Mock sign-in API
export const signInApi = async (data: SignInFormData): Promise<AuthApiResponse<SignInResponse>> => {
  await delay(800);

  // Check if email exists in our password storage (user has signed up)
  const storedPassword = mockPasswords.get(data.email);

  if (!storedPassword) {
    return {
      success: false,
      error: 'Invalid email or password',
    };
  }

  // Check password
  if (data.password !== storedPassword) {
    return {
      success: false,
      error: 'Invalid email or password',
    };
  }

  // Find user by email (might not exist if they haven't completed registration)
  const user = Array.from(mockUsers.values()).find(u => u.email === data.email);

  if (!user) {
    // User signed up but didn't complete the full registration process
    return {
      success: false,
      error: 'Please complete your registration process',
    };
  }

  if (!user.isEmailVerified) {
    return {
      success: false,
      error: 'Please verify your email before signing in',
    };
  }

  const tokens = generateMockTokens(user.id, user.email);

  return {
    success: true,
    data: {
      user,
      tokens,
    },
  };
};

// Mock OTP verification API
export const verifyOTPApi = async (email: string, data: OTPFormData): Promise<AuthApiResponse<OTPVerificationResponse>> => {
  await delay(600);
  
  const storedOTP = mockOTPCodes.get(email);
  
  if (!storedOTP) {
    return {
      success: false,
      error: 'OTP not found or expired',
    };
  }
  
  if (Date.now() > storedOTP.expires) {
    mockOTPCodes.delete(email);
    return {
      success: false,
      error: 'OTP has expired',
    };
  }
  
  if (data.code !== storedOTP.code) {
    return {
      success: false,
      error: 'Invalid OTP code',
    };
  }
  
  // Remove used OTP
  mockOTPCodes.delete(email);
  
  return {
    success: true,
    data: {
      message: 'Email verified successfully',
      isVerified: true,
    },
  };
};

// Mock resend OTP API
export const resendOTPApi = async (email: string): Promise<AuthApiResponse<{ message: string }>> => {
  await delay(500);
  
  // Generate new OTP (use fixed code for development and testing)
  const otpCode = process.env.NODE_ENV === 'production' ? Math.floor(100000 + Math.random() * 900000).toString() : '123456';
  const expires = Date.now() + 10 * 60 * 1000; // 10 minutes
  mockOTPCodes.set(email, { code: otpCode, expires });

  console.log(`Mock OTP resent for ${email}: ${otpCode}`); // For testing purposes
  
  return {
    success: true,
    data: {
      message: 'New confirmation code sent to your email',
    },
  };
};

// Mock business details submission API
export const submitBusinessDetailsApi = async (
  email: string,
  data: BusinessDetailsFormData
): Promise<AuthApiResponse<BusinessDetailsResponse>> => {
  await delay(1000);
  
  // Create new user
  const userId = `user_${Date.now()}`;
  const newUser: User = {
    id: userId,
    email,
    firstName: data.firstName,
    lastName: data.lastName,
    isEmailVerified: true,
    businessDetails: data,
  };
  
  // Store user
  mockUsers.set(userId, newUser);
  
  return {
    success: true,
    data: {
      user: newUser,
      message: 'Account created successfully',
    },
  };
};

// Mock refresh token API
export const refreshTokenApi = async (refreshToken: string): Promise<AuthApiResponse<JWTTokens>> => {
  await delay(300);
  
  // In real implementation, validate refresh token
  if (!refreshToken.startsWith('mock.refresh.token.')) {
    return {
      success: false,
      error: 'Invalid refresh token',
    };
  }
  
  // Extract user info from refresh token (mock implementation)
  const parts = refreshToken.split('.');
  const userId = parts[3];
  const user = mockUsers.get(userId);
  
  if (!user) {
    return {
      success: false,
      error: 'User not found',
    };
  }
  
  const newTokens = generateMockTokens(user.id, user.email);
  
  return {
    success: true,
    data: newTokens,
  };
};

// Mock logout API
export const logoutApi = async (): Promise<AuthApiResponse<{ message: string }>> => {
  await delay(200);
  
  return {
    success: true,
    data: {
      message: 'Logged out successfully',
    },
  };
};

// Helper function to get user by email (for testing)
export const getMockUserByEmail = (email: string): User | undefined => {
  return Array.from(mockUsers.values()).find(user => user.email === email);
};

// Helper function to clear mock data (for testing)
export const clearMockData = () => {
  mockUsers.clear();
  mockOTPCodes.clear();
  mockPasswords.clear();
};
