import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { LoginForm } from './LoginForm';

export interface LoginCardProps {
  onSuccess?: () => void;
}

export function LoginCard({ onSuccess }: LoginCardProps) {
  return (
    <Card className="w-96">
      <CardHeader>
        <CardTitle>Login</CardTitle>
        <CardDescription>Login to your account</CardDescription>
      </CardHeader>
      <CardContent>
        <LoginForm onSuccess={onSuccess} />
      </CardContent>
    </Card>
  );
}
