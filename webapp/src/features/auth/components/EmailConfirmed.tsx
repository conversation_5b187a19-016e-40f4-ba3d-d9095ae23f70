import { Button } from '@/components/ui/button';
import { Check } from 'lucide-react';

interface EmailConfirmedProps {
  onContinue?: () => void;
}

export function EmailConfirmed({ onContinue }: EmailConfirmedProps) {
  return (
    <div className="w-full max-w-md mx-auto">
      {/* Modal-like container */}
      <div className="bg-[#1a1a1a] border border-gray-800 rounded-2xl p-8 text-center space-y-8">
        {/* Success Icon */}
        <div className="flex justify-center">
          <div className="w-20 h-20 bg-primary rounded-full flex items-center justify-center shadow-lg">
            <Check className="w-10 h-10 text-black" strokeWidth={3} />
          </div>
        </div>

        {/* Success Message */}
        <div className="space-y-3">
          <h2 className="text-2xl font-bold text-primary tracking-wide">
            EMAIL CONFIRMED
          </h2>
          <p className="text-gray-400 text-sm">
            Your email address has been successfully verified.
          </p>
        </div>

        {/* Continue Button */}
        <Button
          onClick={onContinue}
          className="w-full h-12 bg-primary text-black hover:bg-[#B8F055] font-medium rounded-lg transition-all duration-200"
        >
          Continue
        </Button>
      </div>
    </div>
  );
}
