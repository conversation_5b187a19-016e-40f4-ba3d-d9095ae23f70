import { describe, it, expect, vi } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import { WalletCreated } from './WalletCreated';

describe('WalletCreated', () => {
  it('should render wallet created success message', () => {
    render(<WalletCreated />);

    expect(screen.getByText('WALLET CREATED')).toBeInTheDocument();
    expect(screen.getByText('Your self-custodial wallet has been successfully created.')).toBeInTheDocument();
  });

  it('should render success icon', () => {
    render(<WalletCreated />);

    // Check for the checkmark icon by looking for the Check component
    const successIcon = document.querySelector('svg');
    expect(successIcon).toBeInTheDocument();

    // Also check for the success message
    expect(screen.getByText('WALLET CREATED')).toBeInTheDocument();
  });

  it('should render continue button', () => {
    render(<WalletCreated />);

    const continueButton = screen.getByRole('button', { name: /let's go!/i });
    expect(continueButton).toBeInTheDocument();
  });

  it('should call onContinue when button clicked', () => {
    const onContinue = vi.fn();
    render(<WalletCreated onContinue={onContinue} />);

    const continueButton = screen.getByRole('button', { name: /let's go!/i });
    fireEvent.click(continueButton);

    expect(onContinue).toHaveBeenCalled();
  });

  it('should have correct styling classes', () => {
    render(<WalletCreated />);

    // Find the modal container (parent of the text)
    const modalContainer = screen.getByText('WALLET CREATED').closest('div')?.parentElement;
    expect(modalContainer).toHaveClass('bg-[#1a1a1a]');
    expect(modalContainer).toHaveClass('border');
    expect(modalContainer).toHaveClass('border-gray-800');
    expect(modalContainer).toHaveClass('rounded-2xl');
  });

  it('should render in modal-like container', () => {
    render(<WalletCreated />);

    // Find the modal container (parent of the text)
    const modalContainer = screen.getByText('WALLET CREATED').closest('div')?.parentElement;
    expect(modalContainer).toHaveClass('bg-[#1a1a1a]');
    expect(modalContainer).toHaveClass('border');
    expect(modalContainer).toHaveClass('border-gray-800');
    expect(modalContainer).toHaveClass('rounded-2xl');
    expect(modalContainer).toHaveClass('p-8');
    expect(modalContainer).toHaveClass('text-center');
  });
});
