import { useAppForm } from '@/components/forms/Form';
import { Button } from '@/components/ui/button';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import { signIn, selectAuthLoading, selectAuthError, clearError } from '@/store/slices/authSlice';
import { useNavigate } from '@tanstack/react-router';
import { useEffect } from 'react';
import z from 'zod/v4';

const loginSchema = z.object({
  email: z.string().email('Please enter a valid email address'),
  password: z.string().min(1, 'Password is required'),
});

export interface LoginFormProps {
  onSuccess?: () => void;
}

export function LoginForm({ onSuccess }: LoginFormProps) {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const isLoading = useAppSelector(selectAuthLoading);
  const error = useAppSelector(selectAuthError);

  const form = useAppForm({
    defaultValues: {
      email: '',
      password: '',
    },
    validators: {
      onChange: loginSchema,
    },
    onSubmit: async ({ value }) => {
      const result = await dispatch(signIn(value));
      if (signIn.fulfilled.match(result)) {
        navigate({ to: '/dashboard' });
        onSuccess?.();
      }
    },
  });
  // Clear error when component unmounts
  useEffect(() => {
    return () => {
      dispatch(clearError());
    };
  }, [dispatch]);

  return (
    <form
      data-testid="login-form"
      onSubmit={(e) => {
        e.preventDefault();
        e.stopPropagation();
        form.handleSubmit();
      }}
      className="space-y-4"
    >
      <div className="space-y-6">
        <form.AppField
          name="email"
          children={(field) => (
            <field.TextField
              label="Email address"
              type="email"
            />
          )}
        />

        <form.AppField
          name="password"
          children={(field) => (
            <field.TextField
              label="Password"
              type="password"
            />
          )}
        />
      </div>

      {error && (
        <div className="text-red-400 text-sm bg-red-900/20 border border-red-800 rounded-lg p-3 text-center">
          {error}
        </div>
      )}

      <Button
        type="submit"
        className="w-full h-12 bg-primary text-black hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed font-medium rounded-lg transition-all duration-200"
        disabled={isLoading || !form.state?.canSubmit}
      >
        {isLoading ? 'Signing In...' : 'Continue'}
      </Button>
    </form>
  );
}
