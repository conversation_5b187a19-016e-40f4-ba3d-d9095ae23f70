import { render, screen } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { LoginCard } from './LoginCard';
import { LoginForm } from './LoginForm';

// Mock the LoginForm component since we want to test LoginCard in isolation
vi.mock('./LoginForm', () => ({
  LoginForm: vi.fn(() => <div data-testid="mock-login-form" />),
}));

describe('LoginCard', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders the login card with correct title and description', () => {
    render(<LoginCard />);

    // Check if the title and description are rendered
    expect(screen.getByText('Login')).toBeInTheDocument();
    expect(screen.getByText('Login to your account')).toBeInTheDocument();
  });

  it('renders the LoginForm component', () => {
    render(<LoginCard />);

    // Check if the LoginForm is rendered
    expect(screen.getByTestId('mock-login-form')).toBeInTheDocument();
  });

  it('passes onSuccess prop to LoginForm', () => {
    const mockOnSuccess = vi.fn();
    render(<LoginCard onSuccess={mockOnSuccess} />);

    // Verify that LoginForm was called with the correct props
    expect(LoginForm).toHaveBeenCalledWith(
      expect.objectContaining({
        onSuccess: mockOnSuccess,
      }),
      expect.any(Object)
    );
  });
});
