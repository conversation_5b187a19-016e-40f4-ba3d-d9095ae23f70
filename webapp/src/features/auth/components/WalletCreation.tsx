import { useState } from 'react';
import { ArrowLeft, Check } from 'lucide-react';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import { createWallet, selectAuthLoading } from '@/store/slices/authSlice';

interface WalletCreationProps {
  onSuccess?: () => void;
  onBack?: () => void;
}

export function WalletCreation({ onSuccess, onBack }: WalletCreationProps) {
  const dispatch = useAppDispatch();
  const isLoading = useAppSelector(selectAuthLoading);

  const [selectedCoin, setSelectedCoin] = useState<string>('tether');
  const [selectedChain, setSelectedChain] = useState<string>('ethereum');
  const [agreedToTerms, setAgreedToTerms] = useState(false);

  const coins = [
    { id: 'tether', name: 'Tether', symbol: 'USDT' }
  ];

  const chains = [
    { id: 'ethereum', name: 'ERC-20', network: 'Ethereum' },
    { id: 'solana', name: 'S<PERSON>', network: 'Solana' },
    { id: 'arbitrum', name: 'ARB', network: 'Arbitrium' }
  ];

  const handleCreateWallet = async () => {
    if (!agreedToTerms) return;

    try {
      await dispatch(createWallet({
        coin: selectedCoin,
        chain: selectedChain
      })).unwrap();
      onSuccess?.();
    } catch (error) {
      console.error('Wallet creation failed:', error);
    }
  };

  return (
    <div className="w-full max-w-md mx-auto space-y-8">
      {/* Header */}
      <div className="space-y-6">
        <h2 className="text-3xl font-semibold text-white flex items-center gap-3">
          {onBack && (
            <button
              type="button"
              onClick={onBack}
              className="text-gray-400 hover:text-white transition-colors"
              disabled={isLoading}
            >
              <ArrowLeft className="w-6 h-6" />
            </button>
          )}
          Let's create you a wallet
        </h2>
      </div>

      {/* Coin Selection */}
      <div className="space-y-4">
        <h3 className="text-white font-medium">Choose your coin</h3>
        <div className="space-y-3">
          {coins.map((coin) => (
            <div
              key={coin.id}
              onClick={() => setSelectedCoin(coin.id)}
              onKeyDown={(e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                  e.preventDefault();
                  setSelectedCoin(coin.id);
                }
              }}
              tabIndex={0}
              role="button"
              className={`p-4 rounded-xl border cursor-pointer transition-all duration-200 ${
                selectedCoin === coin.id
                  ? 'border-primary bg-primary/5'
                  : 'border-gray-700 bg-[#1a1a1a] hover:border-gray-600'
              }`}
            >
              <div className="flex items-center gap-4">
                <div className={`w-6 h-6 rounded-full border-2 flex items-center justify-center ${
                  selectedCoin === coin.id
                    ? 'border-primary bg-primary'
                    : 'border-gray-500'
                }`}>
                  {selectedCoin === coin.id && (
                    <div className="w-3 h-3 bg-black rounded-full" />
                  )}
                </div>
                <div>
                  <div className="text-white font-medium text-base">{coin.name}</div>
                  <div className="text-gray-400 text-sm">{coin.symbol}</div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Chain Selection */}
      <div className="space-y-4">
        <h3 className="text-white font-medium">Choose your chain</h3>
        <div className="grid grid-cols-3 gap-3">
          {chains.map((chain) => (
            <div
              key={chain.id}
              onClick={() => setSelectedChain(chain.id)}
              onKeyDown={(e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                  e.preventDefault();
                  setSelectedChain(chain.id);
                }
              }}
              tabIndex={0}
              role="button"
              className={`p-4 rounded-xl border cursor-pointer transition-all duration-200 ${
                selectedChain === chain.id
                  ? 'border-primary bg-primary/5'
                  : 'border-gray-700 bg-[#1a1a1a] hover:border-gray-600'
              }`}
            >
              <div className="flex flex-col items-center gap-3">
                <div className={`w-6 h-6 rounded-full border-2 flex items-center justify-center ${
                  selectedChain === chain.id
                    ? 'border-primary bg-primary'
                    : 'border-gray-500'
                }`}>
                  {selectedChain === chain.id && (
                    <div className="w-3 h-3 bg-black rounded-full" />
                  )}
                </div>
                <div className="text-center">
                  <div className="text-white font-medium text-sm">{chain.name}</div>
                  <div className="text-gray-400 text-xs">{chain.network}</div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Terms Agreement */}
      <div className="space-y-6">
        <div className="flex items-start gap-3">
          <button
            type="button"
            onClick={() => setAgreedToTerms(!agreedToTerms)}
            className={`w-5 h-5 rounded border-2 flex items-center justify-center transition-all duration-200 ${
              agreedToTerms
                ? 'border-primary bg-primary'
                : 'border-gray-500 hover:border-gray-400'
            }`}
          >
            {agreedToTerms && <Check className="w-3 h-3 text-black" />}
          </button>
          <span
            className="text-gray-300 text-sm cursor-pointer"
            onClick={() => setAgreedToTerms(!agreedToTerms)}
            onKeyDown={(e) => {
              if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                setAgreedToTerms(!agreedToTerms);
              }
            }}
            tabIndex={0}
            role="button"
          >
            I agree to the Terms and Conditions
          </span>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex gap-3">
        {onBack && (
          <button
            type="button"
            onClick={onBack}
            disabled={isLoading}
            className="w-12 h-12 rounded-xl border border-gray-600 text-white hover:border-gray-500 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
          >
            <ArrowLeft className="w-5 h-5" />
          </button>
        )}

        <button
          type="button"
          onClick={handleCreateWallet}
          disabled={!selectedCoin || !selectedChain || !agreedToTerms || isLoading}
          className="flex-1 h-12 bg-primary text-black hover:bg-[#B8F055] disabled:opacity-50 disabled:cursor-not-allowed font-medium rounded-xl transition-all duration-200"
        >
          {isLoading ? 'Creating...' : 'Continue'}
        </button>
      </div>
    </div>
  );
}
