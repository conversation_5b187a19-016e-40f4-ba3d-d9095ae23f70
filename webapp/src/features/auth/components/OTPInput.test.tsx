import { render, screen, fireEvent } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { describe, it, expect, vi } from 'vitest';
import { OTPInput } from './OTPInput';

describe('OTPInput', () => {
  const mockOnChange = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders correct number of input fields', () => {
    render(<OTPInput value="" onChange={mockOnChange} length={6} />);

    for (let i = 0; i < 6; i++) {
      expect(screen.getByTestId(`otp-input-${i}`)).toBeInTheDocument();
    }
  });

  it('displays current value correctly', () => {
    render(<OTPInput value="123" onChange={mockOnChange} length={6} />);

    expect(screen.getByTestId('otp-input-0')).toHaveValue('1');
    expect(screen.getByTestId('otp-input-1')).toHaveValue('2');
    expect(screen.getByTestId('otp-input-2')).toHaveValue('3');
    expect(screen.getByTestId('otp-input-3')).toHaveValue('');
  });

  it('calls onChange when typing digits', async () => {
    const user = userEvent.setup();
    render(<OTPInput value="" onChange={mockOnChange} length={6} />);

    const firstInput = screen.getByTestId('otp-input-0');
    await user.type(firstInput, '1');

    expect(mockOnChange).toHaveBeenCalledWith('1');
  });

  it('moves to next input after entering digit', async () => {
    const user = userEvent.setup();
    render(<OTPInput value="" onChange={mockOnChange} length={6} />);

    const firstInput = screen.getByTestId('otp-input-0');
    const secondInput = screen.getByTestId('otp-input-1');

    await user.type(firstInput, '1');

    expect(document.activeElement).toBe(secondInput);
  });

  it('handles backspace correctly', async () => {
    const user = userEvent.setup();
    render(<OTPInput value="12" onChange={mockOnChange} length={6} />);

    const secondInput = screen.getByTestId('otp-input-1');
    secondInput.focus();

    await user.keyboard('{Backspace}');

    expect(mockOnChange).toHaveBeenCalledWith('1');
  });

  it('handles paste correctly', async () => {
    render(<OTPInput value="" onChange={mockOnChange} length={6} />);

    const firstInput = screen.getByTestId('otp-input-0');
    
    // Simulate paste event
    fireEvent.paste(firstInput, {
      clipboardData: {
        getData: () => '123456',
      },
    });

    expect(mockOnChange).toHaveBeenCalledWith('123456');
  });

  it('ignores non-digit characters', async () => {
    const user = userEvent.setup();
    render(<OTPInput value="" onChange={mockOnChange} length={6} />);

    const firstInput = screen.getByTestId('otp-input-0');
    await user.type(firstInput, 'a');

    expect(mockOnChange).toHaveBeenCalledWith('');
  });

  it('disables inputs when disabled prop is true', () => {
    render(<OTPInput value="" onChange={mockOnChange} disabled={true} length={6} />);

    for (let i = 0; i < 6; i++) {
      expect(screen.getByTestId(`otp-input-${i}`)).toBeDisabled();
    }
  });

  it('applies custom className', () => {
    render(<OTPInput value="" onChange={mockOnChange} className="custom-class" length={6} />);

    const container = screen.getByTestId('otp-input-0').parentElement;
    expect(container).toHaveClass('custom-class');
  });

  it('handles arrow key navigation', async () => {
    const user = userEvent.setup();
    render(<OTPInput value="123" onChange={mockOnChange} length={6} />);

    const secondInput = screen.getByTestId('otp-input-1');
    const firstInput = screen.getByTestId('otp-input-0');
    const thirdInput = screen.getByTestId('otp-input-2');

    secondInput.focus();

    await user.keyboard('{ArrowLeft}');
    expect(document.activeElement).toBe(firstInput);

    await user.keyboard('{ArrowRight}');
    expect(document.activeElement).toBe(secondInput);

    await user.keyboard('{ArrowRight}');
    expect(document.activeElement).toBe(thirdInput);
  });
});
