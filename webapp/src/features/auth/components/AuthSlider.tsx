import { useState, useEffect } from 'react';
import { useAppSelector, useAppDispatch } from '@/store/hooks';
import { selectCurrentStep, setCurrentStep } from '@/store/slices/authSlice';
import invoiceImage from '@/assets/images/523416042ce370de632c87cff79e68579797f152.png';
import paymentImage from '@/assets/images/5df50be752c35cdc6e81308c6a4672d206c472e5.png';
import posImage from '@/assets/images/eeb7dfc1e4ff53b18feb7aad13188fdef135c389.png';

interface Slide {
  id: number;
  title: string;
  image: string;
}

const slides: Slide[] = [
  {
    id: 1,
    title: 'Invoicing',
    image: invoiceImage,
  },
  {
    id: 2,
    title: 'Payments',
    image: paymentImage,
  },
  {
    id: 3,
    title: 'POS Terminal',
    image: posImage,
  },
];

export function AuthSlider() {
  const [currentSlide, setCurrentSlide] = useState(0);

  // Auto-advance slides every 5 seconds (slightly slower for better UX)
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % slides.length);
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  const goToSlide = (index: number) => {
    setCurrentSlide(index);
  };

  return (
    <div
      data-testid="auth-slider"
      className="relative w-full h-full flex flex-col overflow-hidden"
    >
      {/* Main dark background matching Figma exactly */}
      <div className="absolute inset-0 bg-black" />

      {/* Subtle gradient overlay for depth - very minimal */}
      <div className="absolute inset-0 bg-gradient-to-br from-gray-900/20 via-transparent to-gray-800/10" />

      {/* Very subtle radial lighting effect */}
      <div className="absolute inset-0"
           style={{
             background: 'radial-gradient(ellipse 60% 40% at 50% 30%, rgba(20, 20, 20, 0.3) 0%, transparent 70%)'
           }} />

      {/* Slide content */}
      <div className="relative z-10 flex flex-col h-full">
        {/* Title positioned at top left */}
        <div className="absolute top-12 left-12 z-20">
          <h2 className="text-white text-2xl font-semibold transition-all duration-700 ease-out">
            {slides[currentSlide].title}
          </h2>
        </div>

        {/* Main image container - centered and optimized */}
        <div className="flex-1 flex items-center justify-center px-12 py-20">
          <div className="relative w-full max-w-3xl h-[450px] flex items-center justify-center">
            {slides.map((slide, index) => (
              <div
                key={slide.id}
                className={`absolute inset-0 transition-all duration-700 ease-out ${
                  index === currentSlide
                    ? 'opacity-100 transform translate-x-0 scale-100'
                    : index < currentSlide
                      ? 'opacity-0 transform -translate-x-full scale-95'
                      : 'opacity-0 transform translate-x-full scale-95'
                }`}
              >
                <div className="w-full h-full flex items-center justify-center">
                  <img
                    src={slide.image}
                    alt={slide.title}
                    className="max-w-full max-h-full object-contain transition-all duration-700 ease-out hover:scale-105"
                    style={{
                      filter: 'drop-shadow(0 30px 60px rgba(0, 0, 0, 0.7))',
                    }}
                  />
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Navigation dots positioned at bottom center */}
        <div className="absolute bottom-12 left-1/2 transform -translate-x-1/2 z-20">
          <div className="flex space-x-3">
            {slides.map((_, index) => (
              <button
                key={index}
                onClick={() => goToSlide(index)}
                aria-label={`Go to slide ${index + 1}`}
                className={`h-2 rounded-full transition-all duration-300 ease-out ${
                  index === currentSlide
                    ? 'w-8 bg-[#C4FF61] shadow-lg shadow-[#C4FF61]/30'
                    : 'w-2 bg-gray-500 hover:bg-gray-400 hover:w-4'
                }`}
              />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
