import { describe, it, expect, beforeEach, vi } from 'vitest';
import { tokenStorage, jwtUtils, tokenRefreshManager } from './tokenManager';
import { JWTTokens } from '../types';

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
});

describe('Token Manager', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    localStorageMock.getItem.mockReturnValue(null);
  });

  describe('tokenStorage', () => {
    const mockTokens: JWTTokens = {
      accessToken: 'mock.access.token.********************************************************************************************************',
      refreshToken: 'mock.refresh.token.user123.1699999999',
    };

    describe('getTokens', () => {
      it('should return tokens when both exist in localStorage', () => {
        localStorageMock.getItem
          .mockReturnValueOnce(mockTokens.accessToken)
          .mockReturnValueOnce(mockTokens.refreshToken);

        const result = tokenStorage.getTokens();

        expect(result).toEqual(mockTokens);
        expect(localStorageMock.getItem).toHaveBeenCalledWith('wefi_access_token');
        expect(localStorageMock.getItem).toHaveBeenCalledWith('wefi_refresh_token');
      });

      it('should return null when tokens are missing', () => {
        localStorageMock.getItem.mockReturnValue(null);

        const result = tokenStorage.getTokens();

        expect(result).toBeNull();
      });

      it('should handle localStorage errors gracefully', () => {
        localStorageMock.getItem.mockImplementation(() => {
          throw new Error('localStorage error');
        });

        const result = tokenStorage.getTokens();

        expect(result).toBeNull();
      });
    });

    describe('setTokens', () => {
      it('should save tokens to localStorage', () => {
        tokenStorage.setTokens(mockTokens);

        expect(localStorageMock.setItem).toHaveBeenCalledWith('wefi_access_token', mockTokens.accessToken);
        expect(localStorageMock.setItem).toHaveBeenCalledWith('wefi_refresh_token', mockTokens.refreshToken);
      });

      it('should handle localStorage errors gracefully', () => {
        localStorageMock.setItem.mockImplementation(() => {
          throw new Error('localStorage error');
        });

        expect(() => tokenStorage.setTokens(mockTokens)).not.toThrow();
      });
    });

    describe('clearTokens', () => {
      it('should remove tokens from localStorage', () => {
        tokenStorage.clearTokens();

        expect(localStorageMock.removeItem).toHaveBeenCalledWith('wefi_access_token');
        expect(localStorageMock.removeItem).toHaveBeenCalledWith('wefi_refresh_token');
      });
    });
  });

  describe('jwtUtils', () => {
    const mockToken = 'mock.access.token.********************************************************************************************************';
    const mockPayload = {
      sub: 'user123',
      email: '<EMAIL>',
      exp: 1700000000,
      iat: 1699999999,
    };

    describe('decodeToken', () => {
      it('should decode mock token correctly', () => {
        const result = jwtUtils.decodeToken(mockToken);

        expect(result).toEqual(mockPayload);
      });

      it('should return null for invalid token', () => {
        const result = jwtUtils.decodeToken('invalid-token');

        expect(result).toBeNull();
      });

      it('should handle decode errors gracefully', () => {
        const result = jwtUtils.decodeToken('mock.access.token.invalid-base64');

        expect(result).toBeNull();
      });
    });

    describe('isTokenExpired', () => {
      it('should return true for expired token', () => {
        // Create token that expired in the past
        const expiredToken = 'mock.access.token.********************************************************************************************************';
        
        const result = jwtUtils.isTokenExpired(expiredToken);

        expect(result).toBe(true);
      });

      it('should return false for valid token', () => {
        // Create token that expires in the future
        const futureExp = Math.floor(Date.now() / 1000) + 3600; // 1 hour from now
        const validToken = `mock.access.token.${btoa(JSON.stringify({ exp: futureExp }))}`;
        
        const result = jwtUtils.isTokenExpired(validToken);

        expect(result).toBe(false);
      });

      it('should return true for invalid token', () => {
        const result = jwtUtils.isTokenExpired('invalid-token');

        expect(result).toBe(true);
      });
    });

    describe('willExpireSoon', () => {
      it('should return true for token expiring soon', () => {
        // Create token that expires in 2 minutes
        const soonExp = Math.floor(Date.now() / 1000) + 120;
        const soonToken = `mock.access.token.${btoa(JSON.stringify({ exp: soonExp }))}`;
        
        const result = jwtUtils.willExpireSoon(soonToken, 5); // 5 minute threshold

        expect(result).toBe(true);
      });

      it('should return false for token with plenty of time', () => {
        // Create token that expires in 10 minutes
        const laterExp = Math.floor(Date.now() / 1000) + 600;
        const laterToken = `mock.access.token.${btoa(JSON.stringify({ exp: laterExp }))}`;
        
        const result = jwtUtils.willExpireSoon(laterToken, 5); // 5 minute threshold

        expect(result).toBe(false);
      });
    });
  });

  describe('tokenRefreshManager', () => {
    beforeEach(() => {
      tokenRefreshManager.refreshPromise = null;
    });

    describe('refreshIfNeeded', () => {
      it('should return true if token is still valid', async () => {
        // Mock valid tokens
        const futureExp = Math.floor(Date.now() / 1000) + 3600; // 1 hour from now
        const validToken = `mock.access.token.${btoa(JSON.stringify({ exp: futureExp }))}`;
        
        localStorageMock.getItem
          .mockReturnValueOnce(validToken)
          .mockReturnValueOnce('mock.refresh.token.user123.1699999999');

        const result = await tokenRefreshManager.refreshIfNeeded();

        expect(result).toBe(true);
      });

      it('should return false if no tokens exist', async () => {
        localStorageMock.getItem.mockReturnValue(null);

        const result = await tokenRefreshManager.refreshIfNeeded();

        expect(result).toBe(false);
      });

      it('should return false if refresh token is expired', async () => {
        // Mock expired tokens
        const expiredExp = Math.floor(Date.now() / 1000) - 3600; // 1 hour ago
        const expiredToken = `mock.access.token.${btoa(JSON.stringify({ exp: expiredExp }))}`;
        
        localStorageMock.getItem
          .mockReturnValueOnce(expiredToken)
          .mockReturnValueOnce(expiredToken); // Both tokens expired

        const result = await tokenRefreshManager.refreshIfNeeded();

        expect(result).toBe(false);
        expect(localStorageMock.removeItem).toHaveBeenCalled();
      });
    });
  });
});
