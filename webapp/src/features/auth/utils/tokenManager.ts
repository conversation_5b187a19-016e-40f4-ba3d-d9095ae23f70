import { JWTTokens, DecodedJWTPayload } from '../types';

const ACCESS_TOKEN_KEY = 'wefi_access_token';
const REFRESH_TOKEN_KEY = 'wefi_refresh_token';

// Token storage functions
export const tokenStorage = {
  // Get tokens from localStorage
  getTokens(): JWTTokens | null {
    try {
      const accessToken = localStorage.getItem(ACCESS_TOKEN_KEY);
      const refreshToken = localStorage.getItem(REFRESH_TOKEN_KEY);
      
      if (accessToken && refreshToken) {
        return { accessToken, refreshToken };
      }
      return null;
    } catch (error) {
      console.error('Error getting tokens from storage:', error);
      return null;
    }
  },

  // Save tokens to localStorage
  setTokens(tokens: JWTTokens): void {
    try {
      localStorage.setItem(ACCESS_TOKEN_KEY, tokens.accessToken);
      localStorage.setItem(REFRESH_TOKEN_KEY, tokens.refreshToken);
    } catch (error) {
      console.error('Error saving tokens to storage:', error);
    }
  },

  // Remove tokens from localStorage
  clearTokens(): void {
    try {
      localStorage.removeItem(ACCESS_TOKEN_KEY);
      localStorage.removeItem(REFRESH_TOKEN_KEY);
    } catch (error) {
      console.error('Error clearing tokens from storage:', error);
    }
  },

  // Get only access token
  getAccessToken(): string | null {
    try {
      return localStorage.getItem(ACCESS_TOKEN_KEY);
    } catch (error) {
      console.error('Error getting access token from storage:', error);
      return null;
    }
  },

  // Get only refresh token
  getRefreshToken(): string | null {
    try {
      return localStorage.getItem(REFRESH_TOKEN_KEY);
    } catch (error) {
      console.error('Error getting refresh token from storage:', error);
      return null;
    }
  },
};

// JWT token utility functions
export const jwtUtils = {
  // Decode JWT token (mock implementation for development)
  decodeToken(token: string): DecodedJWTPayload | null {
    try {
      // In mock implementation, token format is: mock.access.token.{base64EncodedPayload}
      if (token.startsWith('mock.access.token.')) {
        const encodedPayload = token.split('.')[3];
        const payload = JSON.parse(atob(encodedPayload));
        return payload as DecodedJWTPayload;
      }
      
      // For real JWT tokens, you would use a proper JWT library
      // const payload = jwt.decode(token) as DecodedJWTPayload;
      // return payload;
      
      return null;
    } catch (error) {
      console.error('Error decoding token:', error);
      return null;
    }
  },

  // Check if token is expired
  isTokenExpired(token: string): boolean {
    try {
      const payload = this.decodeToken(token);
      if (!payload) return true;
      
      const currentTime = Math.floor(Date.now() / 1000);
      return payload.exp < currentTime;
    } catch (error) {
      console.error('Error checking token expiration:', error);
      return true;
    }
  },

  // Get token expiration time in milliseconds
  getTokenExpiration(token: string): number | null {
    try {
      const payload = this.decodeToken(token);
      if (!payload) return null;
      
      return payload.exp * 1000; // Convert to milliseconds
    } catch (error) {
      console.error('Error getting token expiration:', error);
      return null;
    }
  },

  // Check if token will expire soon (within 5 minutes)
  willExpireSoon(token: string, thresholdMinutes = 5): boolean {
    try {
      const payload = this.decodeToken(token);
      if (!payload) return true;
      
      const currentTime = Math.floor(Date.now() / 1000);
      const thresholdTime = currentTime + (thresholdMinutes * 60);
      
      return payload.exp < thresholdTime;
    } catch (error) {
      console.error('Error checking if token will expire soon:', error);
      return true;
    }
  },
};

// Token refresh manager
export const tokenRefreshManager = {
  refreshPromise: null as Promise<boolean> | null,

  // Refresh tokens if needed
  async refreshIfNeeded(): Promise<boolean> {
    // Prevent multiple simultaneous refresh attempts
    if (this.refreshPromise) {
      return this.refreshPromise;
    }

    const tokens = tokenStorage.getTokens();
    if (!tokens) {
      return false;
    }

    // Check if access token needs refresh
    if (!jwtUtils.willExpireSoon(tokens.accessToken)) {
      return true; // Token is still valid
    }

    // Check if refresh token is still valid
    if (jwtUtils.isTokenExpired(tokens.refreshToken)) {
      tokenStorage.clearTokens();
      return false; // Refresh token expired, need to login again
    }

    // Perform token refresh
    this.refreshPromise = this.performRefresh();
    const result = await this.refreshPromise;
    this.refreshPromise = null;

    return result;
  },

  // Perform the actual token refresh
  async performRefresh(): Promise<boolean> {
    try {
      // Import here to avoid circular dependency
      const { refreshTokenApi } = await import('../api/mockAuthApi');
      const refreshToken = tokenStorage.getRefreshToken();
      
      if (!refreshToken) {
        return false;
      }

      const response = await refreshTokenApi(refreshToken);
      
      if (response.success && response.data) {
        tokenStorage.setTokens(response.data);
        return true;
      }
      tokenStorage.clearTokens();
      return false;
    } catch (error) {
      console.error('Error refreshing token:', error);
      tokenStorage.clearTokens();
      return false;
    }
  },
};

// Auto-refresh setup
export const setupTokenAutoRefresh = () => {
  // Check token status every minute
  const interval = setInterval(async () => {
    const tokens = tokenStorage.getTokens();
    if (!tokens) {
      clearInterval(interval);
      return;
    }

    // Refresh if token will expire in the next 5 minutes
    if (jwtUtils.willExpireSoon(tokens.accessToken, 5)) {
      const refreshed = await tokenRefreshManager.refreshIfNeeded();
      if (!refreshed) {
        clearInterval(interval);
        // Redirect to login or dispatch logout action
        window.location.href = '/login';
      }
    }
  }, 60000); // Check every minute

  return () => clearInterval(interval);
};

// Initialize token management on app start
export const initializeTokenManagement = () => {
  // Load tokens from storage on app start
  const tokens = tokenStorage.getTokens();
  
  if (tokens) {
    // Check if tokens are still valid
    if (jwtUtils.isTokenExpired(tokens.refreshToken)) {
      // Both tokens expired, clear storage
      tokenStorage.clearTokens();
      return null;
    }
    
    // Setup auto-refresh
    setupTokenAutoRefresh();
    return tokens;
  }
  
  return null;
};
