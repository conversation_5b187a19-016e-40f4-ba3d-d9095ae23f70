import { describe, expect, it, beforeEach, vi, expectTypeOf } from 'vitest';
import { renderHook, waitFor } from '@testing-library/react';
import { usePostsQuery } from './usePostsQuery';
import { QueryClient, QueryClientProvider, UseQueryResult } from '@tanstack/react-query';
import { Post } from '../types';

// Mock data
const mockPosts: Post[] = [
  {
    id: 1,
    title: 'Test Post 1',
    body: 'Test Content 1',
  },
  {
    id: 2,
    title: 'Test Post 2',
    body: 'Test Content 2',
  },
];

// Mock fetch globally
const mockFetch = vi.fn();
global.fetch = mockFetch;

// Create a wrapper component with QueryClient
function createWrapper() {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        // Disable retries for testing
        retry: false,
      },
    },
  });

  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  );
}

describe('usePostsQuery', () => {
  beforeEach(() => {
    mockFetch.mockReset();
  });

  it('should fetch posts successfully', async () => {
    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => mockPosts,
    });

    const { result } = renderHook(() => usePostsQuery(), {
      wrapper: createWrapper(),
    });

    // Initially in loading state
    expect(result.current.isLoading).toBe(true);
    expect(result.current.data).toBeUndefined();

    // Wait for the query to complete
    await waitFor(() => {
      expect(result.current.isSuccess).toBe(true);
    });

    // Verify the data
    expect(result.current.data).toEqual(mockPosts);
    expect(mockFetch).toHaveBeenCalledWith('https://jsonplaceholder.typicode.com/posts');
    expect(mockFetch).toHaveBeenCalledTimes(1);
  });

  it('should handle fetch error', async () => {
    const errorMessage = 'Failed to fetch posts';
    mockFetch.mockRejectedValueOnce(new Error(errorMessage));

    const { result } = renderHook(() => usePostsQuery(), {
      wrapper: createWrapper(),
    });

    // Initially in loading state
    expect(result.current.isLoading).toBe(true);

    // Wait for the query to fail
    await waitFor(() => {
      expect(result.current.isError).toBe(true);
    });

    // Verify error state
    expect(result.current.error).toBeDefined();
    expect(mockFetch).toHaveBeenCalledTimes(1);
  });

  it('should handle empty response', async () => {
    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => [],
    });

    const { result } = renderHook(() => usePostsQuery(), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(result.current.isSuccess).toBe(true);
    });

    expect(result.current.data).toEqual([]);
  });

  it('should handle malformed response', async () => {
    const malformedData = [{ wrongField: 'wrong data' }];
    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => malformedData,
    });

    const { result } = renderHook(() => usePostsQuery(), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(result.current.isSuccess).toBe(true);
    });

    // Even though the data is malformed, TanStack Query will still return it
    // Type checking would catch this at compile time
    expect(result.current.data).toEqual(malformedData);
  });

  it('should handle non-ok response', async () => {
    mockFetch.mockResolvedValueOnce({
      ok: false,
      status: 404,
      statusText: 'Not Found',
    });

    const { result } = renderHook(() => usePostsQuery(), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(result.current.isError).toBe(true);
    });

    expect(mockFetch).toHaveBeenCalledTimes(1);
  });
});

// Type Tests
describe('usePostsQuery types', () => {
  it('should return correct query result type', () => {
    expectTypeOf<ReturnType<typeof usePostsQuery>>().toEqualTypeOf<UseQueryResult<Post[]>>();
  });

  it('should have correct Post type in return type', () => {
    type ExpectedPost = {
      id: string | number;
      title: string;
      body: string;
    };

    type QueryData = ReturnType<typeof usePostsQuery>['data'];
    type UnwrappedData = NonNullable<QueryData>[number];

    expectTypeOf<UnwrappedData>().toMatchTypeOf<ExpectedPost>();
  });
});
