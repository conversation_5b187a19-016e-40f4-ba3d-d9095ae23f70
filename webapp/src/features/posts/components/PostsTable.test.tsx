import { describe, expect, it, expectTypeOf } from 'vitest';
import { render, screen } from '@testing-library/react';
import { PostsTable } from './PostsTable';
import { Post } from '../types';

// Mock data
const mockPosts: Post[] = [
  {
    id: 1,
    title: 'First Post',
    body: 'This is the first post content',
  },
  {
    id: 2,
    title: 'Second Post',
    body: 'This is the second post content',
  },
];

describe('PostsTable', () => {
  it('should render table headers correctly', () => {
    render(<PostsTable posts={[]} />);

    expect(screen.getByRole('columnheader', { name: /title/i })).toBeInTheDocument();
    expect(screen.getByRole('columnheader', { name: /body/i })).toBeInTheDocument();
  });

  it('should render empty table when no posts provided', () => {
    render(<PostsTable posts={[]} />);

    expect(screen.queryByRole('row')).toBeInTheDocument(); // Header row should still be present
    expect(screen.queryAllByRole('row')).toHaveLength(1); // Only header row
  });

  it('should render all posts correctly', () => {
    render(<PostsTable posts={mockPosts} />);

    // Check if all posts are rendered
    mockPosts.forEach((post) => {
      expect(screen.getByText(post.title)).toBeInTheDocument();
      expect(screen.getByText(post.body)).toBeInTheDocument();
    });

    // Check total number of rows (posts + header)
    expect(screen.getAllByRole('row')).toHaveLength(mockPosts.length + 1);
  });

  it('should maintain post order as provided', () => {
    render(<PostsTable posts={mockPosts} />);

    const rows = screen.getAllByRole('row');
    // Skip header row
    const contentRows = rows.slice(1);

    contentRows.forEach((row, index) => {
      const cells = row.querySelectorAll('td');
      expect(cells[0]).toHaveTextContent(mockPosts[index].title);
      expect(cells[1]).toHaveTextContent(mockPosts[index].body);
    });
  });

  it('should handle posts with long content', () => {
    const postsWithLongContent: Post[] = [
      {
        id: 1,
        title: 'A'.repeat(100),
        body: 'B'.repeat(500),
      },
    ];

    render(<PostsTable posts={postsWithLongContent} />);

    expect(screen.getByText('A'.repeat(100))).toBeInTheDocument();
    expect(screen.getByText('B'.repeat(500))).toBeInTheDocument();
  });
});

// Type Tests
describe('PostsTable types', () => {
  it('should have correct prop types', () => {
    type ExpectedProps = {
      posts: Post[];
    };

    expectTypeOf<Parameters<typeof PostsTable>[0]>().toEqualTypeOf<ExpectedProps>();
  });

  it('should have correct Post type structure', () => {
    type ExpectedPost = {
      id: string | number;
      title: string;
      body: string;
    };

    expectTypeOf<Post>().toMatchTypeOf<ExpectedPost>();
  });
});
