import { useQuery } from '@tanstack/react-query';
import { UserDetailResponse } from '../types';

export function useUserDetailQuery(userId: string) {
  return useQuery<UserDetailResponse>({
    queryKey: ['user', userId],
    queryFn: async () => {
      const userData = await fetch(`https://jsonplaceholder.typicode.com/users/${userId}`).then(
        (res) => res.json()
      );
      const userPosts = await fetch(
        `https://jsonplaceholder.typicode.com/posts?userId=${userId}`
      ).then((res) => res.json());
      const userTodos = await fetch(
        `https://jsonplaceholder.typicode.com/todos?userId=${userId}`
      ).then((res) => res.json());
      return { userData, userPosts, userTodos };
    },
  });
}
