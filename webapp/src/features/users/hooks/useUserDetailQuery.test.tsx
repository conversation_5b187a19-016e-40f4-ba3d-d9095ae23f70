import { describe, expect, it, beforeEach, vi } from 'vitest';
import { renderHook, waitFor } from '@testing-library/react';
import { useUserDetailQuery } from './useUserDetailQuery';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { User } from '../types';
import { Post } from '@/lib/types/post';
import { Todo } from '@/lib/types/todo';

// Mock data
const mockUser: User = {
  id: 1,
  name: '<PERSON>',
  email: '<EMAIL>',
  username: 'johndo<PERSON>',
};

const mockPosts: Post[] = [
  {
    id: 1,
    userId: 1,
    title: 'Test Post 1',
    body: 'Test Content 1',
  },
  {
    id: 2,
    userId: 1,
    title: 'Test Post 2',
    body: 'Test Content 2',
  },
];

const mockTodos: Todo[] = [
  {
    id: 1,
    userId: 1,
    title: 'Test Todo 1',
    completed: false,
  },
  {
    id: 2,
    userId: 1,
    title: 'Test Todo 2',
    completed: true,
  },
];

// Mock fetch globally
const mockFetch = vi.fn();
global.fetch = mockFetch;

// Create a wrapper component with QueryClient
function createWrapper() {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        // Disable retries for testing
        retry: false,
      },
    },
  });

  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  );
}

describe('useUserDetailQuery', () => {
  const userId = '1';

  beforeEach(() => {
    mockFetch.mockReset();
  });

  it('should fetch user details successfully', async () => {
    // Mock the three API calls
    mockFetch
      .mockResolvedValueOnce({
        ok: true,
        json: async () => mockUser,
      })
      .mockResolvedValueOnce({
        ok: true,
        json: async () => mockPosts,
      })
      .mockResolvedValueOnce({
        ok: true,
        json: async () => mockTodos,
      });

    const { result } = renderHook(() => useUserDetailQuery(userId), {
      wrapper: createWrapper(),
    });

    // Initially in loading state
    expect(result.current.isLoading).toBe(true);
    expect(result.current.data).toBeUndefined();

    // Wait for all queries to complete
    await waitFor(() => {
      expect(result.current.isSuccess).toBe(true);
    });

    // Verify the data
    expect(result.current.data).toEqual({
      userData: mockUser,
      userPosts: mockPosts,
      userTodos: mockTodos,
    });

    // Verify API calls
    expect(mockFetch).toHaveBeenCalledTimes(3);
    expect(mockFetch).toHaveBeenCalledWith(`https://jsonplaceholder.typicode.com/users/${userId}`);
    expect(mockFetch).toHaveBeenCalledWith(
      `https://jsonplaceholder.typicode.com/posts?userId=${userId}`
    );
    expect(mockFetch).toHaveBeenCalledWith(
      `https://jsonplaceholder.typicode.com/todos?userId=${userId}`
    );
  });

  it('should handle user fetch error', async () => {
    mockFetch.mockRejectedValueOnce(new Error('Failed to fetch user'));

    const { result } = renderHook(() => useUserDetailQuery(userId), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(result.current.isError).toBe(true);
    });

    expect(mockFetch).toHaveBeenCalledTimes(1);
  });

  it('should handle posts fetch error', async () => {
    mockFetch
      .mockResolvedValueOnce({
        ok: true,
        json: async () => mockUser,
      })
      .mockRejectedValueOnce(new Error('Failed to fetch posts'));

    const { result } = renderHook(() => useUserDetailQuery(userId), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(result.current.isError).toBe(true);
    });

    expect(mockFetch).toHaveBeenCalledTimes(2);
  });

  it('should handle todos fetch error', async () => {
    mockFetch
      .mockResolvedValueOnce({
        ok: true,
        json: async () => mockUser,
      })
      .mockResolvedValueOnce({
        ok: true,
        json: async () => mockPosts,
      })
      .mockRejectedValueOnce(new Error('Failed to fetch todos'));

    const { result } = renderHook(() => useUserDetailQuery(userId), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(result.current.isError).toBe(true);
    });

    expect(mockFetch).toHaveBeenCalledTimes(3);
  });

  it('should handle empty responses', async () => {
    mockFetch
      .mockResolvedValueOnce({
        ok: true,
        json: async () => mockUser,
      })
      .mockResolvedValueOnce({
        ok: true,
        json: async () => [], // Empty posts
      })
      .mockResolvedValueOnce({
        ok: true,
        json: async () => [], // Empty todos
      });

    const { result } = renderHook(() => useUserDetailQuery(userId), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(result.current.isSuccess).toBe(true);
    });

    expect(result.current.data).toEqual({
      userData: mockUser,
      userPosts: [],
      userTodos: [],
    });
  });

  it('should handle non-ok responses', async () => {
    mockFetch.mockResolvedValueOnce({
      ok: false,
      status: 404,
      statusText: 'Not Found',
    });

    const { result } = renderHook(() => useUserDetailQuery(userId), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(result.current.isError).toBe(true);
    });

    expect(mockFetch).toHaveBeenCalledTimes(1);
  });
});
