import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Post } from '@/lib/types/post';

interface UserPostsProps {
  posts: Post[];
}

export function UserPosts({ posts }: UserPostsProps) {
  return (
    <Card>
      <CardHeader>
        <h1 className="text-lg font-bold">Posts</h1>
      </CardHeader>
      <CardContent>
        <ul>
          {posts.map((post, index) => (
            <div key={post.id}>
              <h3 className="text-lg font-bold">{post.title}</h3>
              <p className="text-sm text-gray-500">{post.body}</p>
              {index !== posts.length - 1 && <Separator className="mb-4 mt-2" />}
            </div>
          ))}
        </ul>
      </CardContent>
    </Card>
  );
}
