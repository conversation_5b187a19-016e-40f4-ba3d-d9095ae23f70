import { describe, expect, it } from 'vitest';
import { render, screen } from '@testing-library/react';
import { UserPosts } from './UserPosts';
import { Post } from '@/lib/types/post';

const mockPosts: Post[] = [
  {
    id: 1,
    userId: 1,
    title: 'First Post',
    body: 'This is the first post content',
  },
  {
    id: 2,
    userId: 1,
    title: 'Second Post',
    body: 'This is the second post content',
  },
];

describe('UserPosts', () => {
  it('should render posts header', () => {
    render(<UserPosts posts={[]} />);
    expect(screen.getByText('Posts')).toBeInTheDocument();
  });

  it('should render empty state when no posts provided', () => {
    render(<UserPosts posts={[]} />);
    expect(screen.queryByRole('heading', { level: 3 })).not.toBeInTheDocument();
  });

  it('should render all posts with titles and bodies', () => {
    render(<UserPosts posts={mockPosts} />);

    mockPosts.forEach((post) => {
      expect(screen.getByText(post.title)).toBeInTheDocument();
      expect(screen.getByText(post.body)).toBeInTheDocument();
    });
  });

  it('should render separators between posts except for the last one', () => {
    render(<UserPosts posts={mockPosts} />);

    // There should be one less separator than posts
    const separators = document.querySelectorAll('.mb-4.mt-2');
    expect(separators).toHaveLength(mockPosts.length - 1);
  });

  it('should handle posts with long content', () => {
    const longPosts: Post[] = [
      {
        id: 1,
        userId: 1,
        title: 'A'.repeat(100),
        body: 'B'.repeat(500),
      },
    ];

    render(<UserPosts posts={longPosts} />);
    expect(screen.getByText('A'.repeat(100))).toBeInTheDocument();
    expect(screen.getByText('B'.repeat(500))).toBeInTheDocument();
  });
});
