import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Todo } from '@/lib/types/todo';
import { CheckCircle2, Circle } from 'lucide-react';

interface UserTodosProps {
  todos: Todo[];
}

export function UserTodos({ todos }: UserTodosProps) {
  return (
    <Card>
      <CardHeader>
        <h1 className="text-lg font-bold">Todos</h1>
      </CardHeader>
      <CardContent>
        <ul className="space-y-2">
          {todos.map((todo) => (
            <li
              key={todo.id}
              className={`flex items-start gap-2 ${
                todo.completed ? 'text-gray-500' : 'text-gray-900'
              }`}
            >
              {todo.completed ? (
                <CheckCircle2 className="h-5 w-5 text-green-500 flex-shrink-0 mt-0.5" />
              ) : (
                <Circle className="h-5 w-5 text-gray-400 flex-shrink-0 mt-0.5" />
              )}
              <span className={todo.completed ? 'line-through' : ''}>{todo.title}</span>
            </li>
          ))}
        </ul>
      </CardContent>
    </Card>
  );
}
