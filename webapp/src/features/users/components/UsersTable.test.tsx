import { describe, expect, it, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import { UsersTable } from './UsersTable';
import { User } from '../types';
import { useNavigate } from '@tanstack/react-router';

// Mock the navigation
vi.mock('@tanstack/react-router', () => ({
  useNavigate: vi.fn(),
}));

const mockUsers: User[] = [
  {
    id: 1,
    name: '<PERSON>',
    email: '<EMAIL>',
    username: 'johndo<PERSON>',
  },
  {
    id: 2,
    name: '<PERSON>',
    email: '<EMAIL>',
    username: 'jane<PERSON>',
  },
];

describe('UsersTable', () => {
  const mockNavigate = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
    (useNavigate as any).mockReturnValue(mockNavigate);
  });

  it('should render table headers correctly', () => {
    render(<UsersTable users={[]} />);

    // Use exact text matches instead of regex to avoid ambiguity
    const headers = screen.getAllByRole('columnheader');
    expect(headers).toHaveLength(3);
    expect(headers[0]).toHaveTextContent('Name');
    expect(headers[1]).toHaveTextContent('Email');
    expect(headers[2]).toHaveTextContent('Username');
  });

  it('should render empty table when no users provided', () => {
    render(<UsersTable users={[]} />);

    expect(screen.queryByRole('row')).toBeInTheDocument(); // Header row should still be present
    expect(screen.queryAllByRole('row')).toHaveLength(1); // Only header row
  });

  it('should render all users with correct information', () => {
    render(<UsersTable users={mockUsers} />);

    mockUsers.forEach((user) => {
      expect(screen.getByText(user.name)).toBeInTheDocument();
      expect(screen.getByText(user.email)).toBeInTheDocument();
      expect(screen.getByText(user.username)).toBeInTheDocument();
    });

    // Check total number of rows (users + header)
    expect(screen.getAllByRole('row')).toHaveLength(mockUsers.length + 1);
  });

  it('should navigate to user details when row is clicked', () => {
    render(<UsersTable users={mockUsers} />);

    const firstUserRow = screen.getByText(mockUsers[0].name).closest('tr');
    fireEvent.click(firstUserRow!);

    expect(mockNavigate).toHaveBeenCalledWith({
      to: '/users/$userId',
      params: { userId: mockUsers[0].id.toString() },
    });
  });

  it('should apply hover styles to rows', () => {
    render(<UsersTable users={mockUsers} />);

    const userRows = screen.getAllByRole('row').slice(1); // Skip header row
    userRows.forEach((row) => {
      expect(row).toHaveClass('hover:bg-gray-100', 'cursor-pointer');
    });
  });

  it('should maintain user order as provided', () => {
    render(<UsersTable users={mockUsers} />);

    const rows = screen.getAllByRole('row');
    // Skip header row
    const contentRows = rows.slice(1);

    contentRows.forEach((row, index) => {
      const cells = row.querySelectorAll('td');
      expect(cells[0]).toHaveTextContent(mockUsers[index].name);
      expect(cells[1]).toHaveTextContent(mockUsers[index].email);
      expect(cells[2]).toHaveTextContent(mockUsers[index].username);
    });
  });
});
