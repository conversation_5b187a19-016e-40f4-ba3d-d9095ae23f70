import { describe, expect, it } from 'vitest';
import { render, screen } from '@testing-library/react';
import { UserTodos } from './UserTodos';
import { Todo } from '@/lib/types/todo';

const mockTodos: Todo[] = [
  {
    id: 1,
    userId: 1,
    title: 'Complete task 1',
    completed: false,
  },
  {
    id: 2,
    userId: 1,
    title: 'Complete task 2',
    completed: true,
  },
];

describe('UserTodos', () => {
  it('should render todos header', () => {
    render(<UserTodos todos={[]} />);
    expect(screen.getByText('Todos')).toBeInTheDocument();
  });

  it('should render empty state when no todos provided', () => {
    render(<UserTodos todos={[]} />);
    expect(screen.queryByRole('listitem')).not.toBeInTheDocument();
  });

  it('should render all todos with correct completion status', () => {
    render(<UserTodos todos={mockTodos} />);

    // Check incomplete todo
    const incompleteTodo = mockTodos[0];
    const incompleteTodoElement = screen.getByText(incompleteTodo.title);
    expect(incompleteTodoElement).toBeInTheDocument();
    expect(incompleteTodoElement).not.toHaveClass('line-through');
    expect(incompleteTodoElement.parentElement).toHaveClass('text-gray-900');

    // Check complete todo
    const completeTodo = mockTodos[1];
    const completeTodoElement = screen.getByText(completeTodo.title);
    expect(completeTodoElement).toBeInTheDocument();
    expect(completeTodoElement).toHaveClass('line-through');
    expect(completeTodoElement.parentElement).toHaveClass('text-gray-500');
  });

  it('should render correct icons based on completion status', () => {
    render(<UserTodos todos={mockTodos} />);

    // Check icons for incomplete todo
    const incompleteIcons = document.querySelectorAll('.text-gray-400');
    expect(incompleteIcons).toHaveLength(1);

    // Check icons for complete todo
    const completeIcons = document.querySelectorAll('.text-green-500');
    expect(completeIcons).toHaveLength(1);
  });

  it('should handle todos with long titles', () => {
    const longTodos: Todo[] = [
      {
        id: 1,
        userId: 1,
        title: 'A'.repeat(100),
        completed: false,
      },
    ];

    render(<UserTodos todos={longTodos} />);
    expect(screen.getByText('A'.repeat(100))).toBeInTheDocument();
  });

  it('should maintain vertical spacing between todos', () => {
    render(<UserTodos todos={mockTodos} />);

    const todoList = screen.getByRole('list');
    expect(todoList).toHaveClass('space-y-2');
  });
});
