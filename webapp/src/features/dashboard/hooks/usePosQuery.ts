import { useEffect, useState } from 'react';
import { POSItem } from '../types';

interface PosQueryResult {
  data: POSItem[] | null;
  isLoading: boolean;
  isError: boolean;
}

export function usePosQuery(): PosQueryResult {
  const [data, setData] = useState<POSItem[] | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isError, setIsError] = useState(false);

  useEffect(() => {
    const fetchPos = async () => {
      try {
        setIsLoading(true);
        setIsError(false);

        // TODO: Replace with actual API call
        const mockData: POSItem[] = [
          {
            id: '1',
            name: 'POS Devices',
            amount: 10,
            type: 'DEVICE',
            paired: 8,
            unpaired: 2,
          },
          {
            id: '2',
            name: 'API',
            amount: 5,
            type: 'API',
            active: 5,
          },
          {
            id: '3',
            name: 'Ecommerce Plugins',
            amount: 4,
            type: 'ECOMMERCE',
            connected: 4,
          },
          {
            id: '4',
            name: 'Invoicing',
            amount: 314,
            type: 'INVOICE',
            total: 314,
            pending: 12,
          },
          {
            id: '5',
            name: 'QR Codes',
            amount: 41,
            type: 'QR',
            total: 41,
          },
        ];

        // Simulate API delay
        await new Promise((resolve) => setTimeout(resolve, 1000));
        setData(mockData);
      } catch (_) {
        setIsError(true);
        setData(null);
      } finally {
        setIsLoading(false);
      }
    };

    fetchPos();
  }, []);

  return { data, isLoading, isError };
}
