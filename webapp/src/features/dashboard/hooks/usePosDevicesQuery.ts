import { useEffect, useState } from 'react';
import { POSDevice } from '../types';

interface PosDevicesQueryResult {
  data: POSDevice[] | null;
  isLoading: boolean;
  isError: boolean;
}

export function usePosDevicesQuery(): PosDevicesQueryResult {
  const [data, setData] = useState<POSDevice[] | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isError, setIsError] = useState(false);

  useEffect(() => {
    const fetchPosDevices = async () => {
      try {
        setIsLoading(true);
        setIsError(false);

        // TODO: Replace with actual API call
        const mockData: POSDevice[] = [
          {
            id: '1',
            businessName: 'POS Name',
            address: 'Address line',
            status: 'PAIRED',
            dateAdded: Date.now(),
            type: 'Terminal',
            country: 'AE',
          },
          {
            id: '2',
            businessName: 'POS Name',
            address: 'Address line',
            status: 'PAIRED',
            dateAdded: Date.now(),
            type: 'Terminal',
            country: 'AE',
          },
          {
            id: '3',
            businessName: 'POS Name',
            address: 'Address line',
            status: 'PAIRED',
            dateAdded: Date.now(),
            type: 'Terminal',
            country: 'AE',
          },
          {
            id: '4',
            businessName: 'POS Name',
            address: 'Address line',
            status: 'UNPAIRED',
            dateAdded: Date.now(),
            type: 'Mobile',
            country: 'AE',
          },
          {
            id: '5',
            businessName: 'POS Name',
            address: 'Address line',
            status: 'PAIRED',
            dateAdded: Date.now(),
            type: 'Terminal',
            country: 'AE',
          },
        ];

        // Simulate API delay
        await new Promise((resolve) => setTimeout(resolve, 1000));
        setData(mockData);
      } catch (_) {
        setIsError(true);
        setData(null);
      } finally {
        setIsLoading(false);
      }
    };

    fetchPosDevices();
  }, []);

  return { data, isLoading, isError };
}
