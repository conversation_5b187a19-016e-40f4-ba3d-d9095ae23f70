import { describe, it, expect } from 'vitest'
import { render, screen, waitFor } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { usePaymentsQuery } from './usePaymentsQuery'

function TestComponent() {
    const { data, isLoading, isError } = usePaymentsQuery()

    return (
        <div>
            <div data-testid="status">
                {isLoading ? 'loading' : isError ? 'error' : 'done'}
            </div>
            <div data-testid="count">
                {data ? data.length : 0}
            </div>
        </div>
    )
}

describe('usePaymentsQuery', () => {
    it('should fetch payments and not be loading after success', async () => {
        const queryClient = new QueryClient({
            defaultOptions: {
                queries: {
                    retry: false,
                    gcTime: 0,
                    refetchOnWindowFocus: false,
                },
            },
        })

        render(
            <QueryClientProvider client={queryClient}>
                <TestComponent />
            </QueryClientProvider>
        )

        // First, verify loading state
        expect(screen.getByTestId('status').textContent).toBe('loading')

        // Then wait for it to finish
        await waitFor(() => {
            expect(screen.getByTestId('status').textContent).toBe('done')
        }, {
            timeout: 2000
        })

        const count = Number(screen.getByTestId('count').textContent)
        expect(count).toBeGreaterThan(0)
    })

    it('should handle empty data state', async () => {
        const queryClient = new QueryClient({
            defaultOptions: {
                queries: {
                    retry: false,
                    gcTime: 0,
                    refetchOnWindowFocus: false,
                },
            },
        })

        render(
            <QueryClientProvider client={queryClient}>
                <TestComponent />
            </QueryClientProvider>
        )

        // First, verify loading state
        expect(screen.getByTestId('status').textContent).toBe('loading')
        expect(screen.getByTestId('count').textContent).toBe('0')

        // Then wait for it to finish
        await waitFor(() => {
            expect(screen.getByTestId('status').textContent).toBe('done')
        }, {
            timeout: 2000
        })
    })
})
