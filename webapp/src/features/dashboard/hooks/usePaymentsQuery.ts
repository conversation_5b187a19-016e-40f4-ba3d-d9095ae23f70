import { useEffect, useState } from 'react';
import { Payment } from '../types';

interface PaymentsQueryResult {
  data: Payment[] | null;
  isLoading: boolean;
  isError: boolean;
}

export function usePaymentsQuery(): PaymentsQueryResult {
  const [data, setData] = useState<Payment[] | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isError, setIsError] = useState(false);

  useEffect(() => {
    const fetchPayments = async () => {
      try {
        setIsLoading(true);
        setIsError(false);

        // TODO: Replace with actual API call
        const mockData: Payment[] = [
          {
            id: '1',
            address: '0x0b123456789ABC123456789ABC123456789ABCd',
            amount: 1500.5,
            date: '2025-05-26',
            payment_method: 'POS',
            coin: {
              coin_id: 'usdt-bep20',
              name_short: 'USDT',
            },
            application: {
              name: 'Online Store',
            },
            created_at: Date.now(),
            processed_at: Date.now(),
            amount_fiat: 1500.5,
            currency_fiat: 'USD',
            status: 'PAID',
          },
          {
            id: '2',
            address: '0x0b123456789ABC123456789ABC123456789ABCd',
            amount: 500.0,
            date: '2025-05-25',
            payment_method: 'QR',
            coin: {
              coin_id: 'usdt-bep20',
              name_short: 'USDT',
            },
            application: {
              name: 'Service Payment',
            },
            created_at: Date.now() - 86400000,
            processed_at: Date.now() - 86400000,
            amount_fiat: 500.0,
            currency_fiat: 'USD',
            status: 'PENDING',
          },
          {
            id: '3',
            address: '0x0b123456789ABC123456789ABC123456789ABCd',
            amount: 2550,
            date: '2025-05-24',
            payment_method: 'Invoice',
            coin: {
              coin_id: 'usdt-trc20',
              name_short: 'USDT',
            },
            application: {
              name: 'POS Terminal #42',
            },
            created_at: Date.now() - 86400000 * 2,
            processed_at: Date.now() - 86400000 * 2,
            amount_fiat: 2550,
            currency_fiat: 'USD',
            status: 'EXPIRED',
          },
          {
            id: '4',
            address: '0x0b123456789ABC123456789ABC123456789ABCd',
            amount: 250,
            date: '2025-05-21',
            payment_method: 'API',
            coin: {
              coin_id: 'usdt-trc20',
              name_short: 'USDT',
            },
            application: {
              name: 'POS Terminal #43',
            },
            created_at: Date.now() - 86400000 * 5,
            processed_at: Date.now() - 86400000 * 5,
            amount_fiat: 250,
            currency_fiat: 'USD',
            status: 'UNDERPAID',
          },
        ];

        // Simulate API delay
        await new Promise((resolve) => setTimeout(resolve, 1000));
        setData(mockData);
      } catch (_) {
        setIsError(true);
        setData(null);
      } finally {
        setIsLoading(false);
      }
    };

    fetchPayments();
  }, []);

  return { data, isLoading, isError };
}
