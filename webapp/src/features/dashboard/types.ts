// TODO: replace with the actual data structure

// Payment method types
export type PaymentMethod = 'POS' | 'QR' | 'Invoice' | 'API';

// Payment status types
export type PaymentStatus =
  | 'PAID'
  | 'PENDING'
  | 'EXPIRED'
  | 'CANCELED'
  | 'UNDERPAID'
  | 'REFUNDED'
  | 'FAILED'
  | 'BLOCKED';

export interface Payment {
  id: string;
  address: string;
  amount: number;
  date: string;
  payment_method: PaymentMethod;
  coin?: {
    coin_id: string;
    name_short: string;
  };
  application?: {
    name: string;
  };
  created_at?: number;
  processed_at?: number;
  amount_fiat?: number;
  currency_fiat?: string;
  status?: PaymentStatus;
}

export interface BalanceItem {
  id: string;
  name: string;
  currency: string;
  coin_id: string;
  amount: number;
}

// POS types
export type PosMethod = 'DEVICE' | 'QR' | 'INVOICE' | 'API' | 'ECOMMERCE';

export interface POSItem {
  id: string;
  name: string;
  amount: number;
  type: PosMethod;
  paired?: number;
  unpaired?: number;
  active?: number;
  connected?: number;
  pending?: number;
  total?: number;
}

// Pos devices types
export type PosDeviceType = 'Mobile' | 'Terminal';

// Pos devices status types
export type PosDeviceStatus = 'PAIRED' | 'UNPAIRED';

export interface POSDevice {
  id: string;
  businessName: string;
  address: string;
  country?: string;
  status: PosDeviceStatus;
  dateAdded: number;
  type: PosDeviceType;
}

export interface DashboardData {
  paymentsData: Payment[];
  balanceData: BalanceItem[];
  posData: POSItem[];
}
