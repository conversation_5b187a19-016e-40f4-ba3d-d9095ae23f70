import { cn } from '@/lib/utils';
import { Link } from '@tanstack/react-router';
import { forwardRef } from 'react';

interface DashboardCardProps {
  title: string;
  route?: string;
  children: React.ReactNode;
  isEmpty?: boolean;
  emptyMessage?: string;
  className?: string;
}

export const DashboardCard = forwardRef<HTMLDivElement, DashboardCardProps>(function DashboardCard(
  { title, route, children, isEmpty = false, emptyMessage = 'There are no data yet.', className },
  ref
) {
  return (
    <div
      ref={ref}
      className={cn('rounded-2xl bg-card text-card-foreground shadow-sm p-7', className)}
    >
      <div className="flex items-center justify-between pb-4">
        <h3 className="text-lg text-font-space-grotesk leading-none tracking-tight">{title}</h3>
        {route && (
          <Link to={route} className="text-sm text-primary transition-colors">
            See All
          </Link>
        )}
      </div>
      <div>
        {isEmpty ? (
          <div className="flex items-center justify-center py-8">
            <p className="text-sm text-muted-foreground">{emptyMessage}</p>
          </div>
        ) : (
          children
        )}
      </div>
    </div>
  );
});
