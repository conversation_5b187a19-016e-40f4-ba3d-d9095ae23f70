import { Button } from '@/components/ui/button';

interface EmptyStateProps {
  type: 'new' | 'filtered' | 'search' | 'error';
  context: 'pos' | 'payments';
  onClearFilters?: () => void;
  searchTerm?: string;
}

export function EmptyState({ type, context, onClearFilters }: EmptyStateProps) {
  const getContent = () => {
    const contentMap = {
      pos: {
        new: {
          message:
            'No devices added yet. Start by connecting a POS terminal or mobile device to begin accepting crypto.',
          suggestion: 'Add New Device',
        },
        filtered: {
          message: 'No devices match your filter criteria.',
          suggestion: '',
        },
        search: {
          message: 'No results found for your search.',
          suggestion: 'Check spelling or try a different value',
        },
        error: {
          message: 'Unable to load devices. Please try again.',
          suggestion: 'Retry',
        },
      },
      payments: {
        new: {
          message:
            'No payments yet. Once a customer pays via POS, QR code, or invoice, transactions will appear here.',
          suggestion: null,
        },
        filtered: {
          message: 'No transactions match your filters.',
          suggestion: '',
        },
        search: {
          message: 'No results found for your search.',
          suggestion: 'Check spelling or try a different value',
        },
        error: {
          message: 'Unable to load transactions.',
          suggestion: 'Retry',
        },
      },
    };

    return contentMap[context][type];
  };

  const content = getContent();

  return (
    <div className="flex flex-col items-center justify-center py-8 text-center">
      <p className="text-lg text-foreground mb-2">{content.message}</p>
      {content.suggestion && (
        <p className="text-sm text-muted-foreground mb-4">{content.suggestion}</p>
      )}
      {type === 'filtered' && onClearFilters && (
        <Button variant="outline" onClick={onClearFilters}>
          Clear filters
        </Button>
      )}
    </div>
  );
}
