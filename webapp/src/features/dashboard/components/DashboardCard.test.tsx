import { describe, it, expect, vi } from 'vitest';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import { DashboardCard } from './DashboardCard';

vi.mock('@tanstack/react-router', () => ({
  Link: vi.fn(({ to, children, className }: { to: string; children: React.ReactNode; className?: string }) => (
    <a href={to} className={className} data-testid="router-link">
      {children}
    </a>
  ))
}));

describe('DashboardCard', () => {
  it('renders title and children', () => {
    render(
        <DashboardCard title="Test Card">
          <div>Test Content</div>
        </DashboardCard>
    );

    expect(screen.getByText('Test Card')).toBeInTheDocument();
    expect(screen.getByText('Test Content')).toBeInTheDocument();
  });

  it('renders route link when provided', () => {
    render(
        <DashboardCard title="Test Card" route="/test">
          <div>Test Content</div>
        </DashboardCard>
    );

    const link = screen.getByTestId('router-link');
    expect(link).toHaveAttribute('href', '/test');
    expect(link).toHaveClass('text-sm', 'text-primary');
    expect(link).toHaveTextContent('See All');
  });

  it('shows empty state when isEmpty is true', () => {
    render(
        <DashboardCard title="Test Card" isEmpty>
          <div>Test Content</div>
        </DashboardCard>
    );

    expect(screen.getByText('There are no data yet.')).toBeInTheDocument();
    expect(screen.queryByText('Test Content')).not.toBeInTheDocument();
  });

  it('shows custom empty message when provided', () => {
    const customMessage = 'No items available';
    render(
      <DashboardCard title="Test Card" isEmpty emptyMessage={customMessage}>
        <div>Test Content</div>
      </DashboardCard>
    );

    expect(screen.getByText(customMessage)).toBeInTheDocument();
    expect(screen.queryByText('There are no data yet.')).not.toBeInTheDocument();
  });
}); 