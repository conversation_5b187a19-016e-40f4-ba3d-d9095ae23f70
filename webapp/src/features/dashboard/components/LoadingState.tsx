import { Loader } from '@/assets/icons';
import { useState, useEffect } from 'react';

interface LoadingSpinnerProps {
  showSlowLoadingDelay?: number;
}

export function LoadingState({ showSlowLoadingDelay = 10000 }: LoadingSpinnerProps) {
  const [showSlowLoadingMessage, setShowSlowLoadingMessage] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => {
      setShowSlowLoadingMessage(true);
    }, showSlowLoadingDelay);

    return () => {
      clearTimeout(timer);
      setShowSlowLoadingMessage(false);
    };
  }, [showSlowLoadingDelay]);

  return (
    <div className="flex flex-col items-center justify-center h-64">
      <Loader />
      <div className="h-24 flex flex-col items-center justify-center text-center">
        {showSlowLoadingMessage && (
          <div className="py-8">
            <p className="mt-4 text-muted-foreground">Loading taking longer than expected...</p>
            <p className="text-sm text-muted-foreground">Please wait while we fetch your data.</p>
          </div>
        )}
      </div>
    </div>
  );
}
