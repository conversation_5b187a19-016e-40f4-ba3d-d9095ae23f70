import { render, screen, act } from '@testing-library/react';
import { describe, expect, it, vi, beforeEach, afterEach } from 'vitest';
import { LoadingState } from './LoadingState';

// Mock the Loader component
vi.mock('@/assets/icons', () => ({
  Loader: () => <div data-testid="loader-icon">Loading Icon</div>
}));

describe('LoadingState', () => {
  beforeEach(() => {
    vi.useFakeTimers();
  });

  afterEach(() => {
    vi.useRealTimers();
    vi.clearAllTimers();
  });

  it('renders loading spinner initially', () => {
    render(<LoadingState />);
    
    // Check if the loader icon is rendered
    expect(screen.getByTestId('loader-icon')).toBeInTheDocument();
    
    // Verify that the slow loading message is not shown initially
    expect(screen.queryByText('Loading taking longer than expected...')).not.toBeInTheDocument();
  });

  it('shows slow loading message after delay', () => {
    // Use a small delay for testing purposes
    render(<LoadingState showSlowLoadingDelay={100} />);
    
    // Initially, the message should not be visible
    expect(screen.queryByText('Loading taking longer than expected...')).not.toBeInTheDocument();
    
    // Advance timers to trigger the delay
    act(() => {
      vi.advanceTimersByTime(100);
    });
    
    // Check that the message is now visible
    expect(screen.getByText('Loading taking longer than expected...')).toBeInTheDocument();
    expect(screen.getByText('Please wait while we fetch your data.')).toBeInTheDocument();
  });
});