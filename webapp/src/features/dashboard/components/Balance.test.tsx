import { describe, it, expect, vi } from 'vitest';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import { Balance } from './Balance';
import { BalanceItem } from '../types';

vi.mock('@tanstack/react-router', () => ({
  Link: vi.fn(({ to, children, className }: { to: string; children: React.ReactNode; className?: string }) => (
    <a href={to} className={className} data-testid="router-link">
      {children}
    </a>
  ))
}));

const mockBalanceData: BalanceItem[] = [
  {
    id: '1',
    name: 'Bitcoin',
    currency: 'BTC',
    coin_id: 'btc',
    amount: 1.5
  },
  {
    id: '2',
    name: 'Ethereum',
    currency: 'ETH',
    coin_id: 'eth',
    amount: 10
  }
];

describe('Balance', () => {
  it('renders balance items correctly', () => {
    render(<Balance data={mockBalanceData} />);

    expect(screen.getByText('Bitcoin')).toBeInTheDocument();
    expect(screen.getByText('1.50 BTC')).toBeInTheDocument();

    expect(screen.getByText('Ethereum')).toBeInTheDocument();
    expect(screen.getByText('10.00 ETH')).toBeInTheDocument();
  });

  it('renders empty state when no data provided', () => {
    render(<Balance data={[]} />);
    expect(screen.getByText('There are no data yet.')).toBeInTheDocument();
  });

  it('renders correct number of balance items', () => {
    render(<Balance data={mockBalanceData} />);
    const balanceItems = screen.getAllByRole('listitem');
    expect(balanceItems).toHaveLength(2);
  });

  it('handles single balance item correctly', () => {
    const singleBalanceData: BalanceItem[] = [{
      id: '1',
      name: 'Bitcoin',
      currency: 'BTC',
      coin_id: 'btc',
      amount: 1.0
    }];

    render(<Balance data={singleBalanceData} />);
    
    expect(screen.getByText('Bitcoin')).toBeInTheDocument();
    expect(screen.getByText('1.00 BTC')).toBeInTheDocument();
    expect(screen.getAllByRole('listitem')).toHaveLength(1);
  });

  it('shows "0.00" for zero amounts', () => {
    const zeroBalanceData: BalanceItem[] = [{
      id: '1',
      name: 'Bitcoin',
      currency: 'BTC',
      coin_id: 'btc',
      amount: 0
    }];

    render(<Balance data={zeroBalanceData} />);
    
    expect(screen.getByText('Bitcoin')).toBeInTheDocument();
    expect(screen.getByText('0.00 BTC')).toBeInTheDocument();
  });

  it('renders non-zero amounts with correct formatting', () => {
    render(<Balance data={mockBalanceData} />);
    
    mockBalanceData.forEach(item => {
      const formattedAmount = `${item.amount.toFixed(2)} ${item.currency}`;
      expect(screen.getByText(formattedAmount)).toBeInTheDocument();
    });
  });

  it('mixes zero and non-zero amounts correctly', () => {
    const mixedBalanceData: BalanceItem[] = [
      {
        id: '1',
        name: 'Bitcoin',
        currency: 'BTC',
        coin_id: 'btc',
        amount: 1.5
      },
      {
        id: '2',
        name: 'Ethereum',
        currency: 'ETH',
        coin_id: 'eth',
        amount: 0
      }
    ];

    render(<Balance data={mixedBalanceData} />);
    
    expect(screen.getByText('1.50 BTC')).toBeInTheDocument();
    expect(screen.getByText('0.00 ETH')).toBeInTheDocument();
  });
}); 