import { BalanceItem } from '../types';
import { DashboardCard } from './DashboardCard';

interface BalanceProps {
  data: BalanceItem[];
}

export function Balance({ data }: BalanceProps) {
  const formatAmount = (amount: number, currency: string) => `${amount.toFixed(2)} ${currency}`;

  return (
    <DashboardCard title="Balance" route="/balance" isEmpty={!data.length}>
      <div className="space-y-4">
        {data.map((item) => (
          <div
            key={item.id}
            role="listitem"
            className="flex items-center gap-4 p-3 rounded-sm bg-muted/50"
          >
            <img
              src={`https://content.ivendpay.com/pic/wefi/${item.coin_id}.png`}
              className="w-10 h-10 rounded-full bg-background object-contain p-1"
              alt={item.name}
            />
            <div className="flex-1 min-w-0">
              <p>{item.name}</p>
              <p className="font-bold text-popover-foreground">
                {formatAmount(item.amount, item.currency)}
              </p>
            </div>
          </div>
        ))}
      </div>
    </DashboardCard>
  );
}
