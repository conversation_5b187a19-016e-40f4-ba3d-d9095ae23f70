import { describe, it, expect, vi } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import { EmptyState } from './EmptyState';

describe('EmptyState', () => {
  it('renders POS new state correctly', () => {
    render(<EmptyState type="new" context="pos" />);

    expect(screen.getByText('No devices added yet. Start by connecting a POS terminal or mobile device to begin accepting crypto.')).toBeInTheDocument();
    expect(screen.getByText('Add New Device')).toBeInTheDocument();
  });

  it('renders payments new state correctly', () => {
    render(<EmptyState type="new" context="payments" />);

    expect(screen.getByText('No payments yet. Once a customer pays via POS, QR code, or invoice, transactions will appear here.')).toBeInTheDocument();
    expect(screen.queryByText('Add New Device')).not.toBeInTheDocument();
  });

  it('renders filtered state with button', () => {
    const mockClearFilters = vi.fn();
    render(<EmptyState type="filtered" context="pos" onClearFilters={mockClearFilters} />);

    expect(screen.getByText('No devices match your filter criteria.')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: 'Clear filters' })).toBeInTheDocument();
  });

  it('calls onClearFilters when clicked', () => {
    const mockClearFilters = vi.fn();
    render(<EmptyState type="filtered" context="payments" onClearFilters={mockClearFilters} />);

    expect(screen.getByText('No transactions match your filters.')).toBeInTheDocument();
    
    const clearButton = screen.getByRole('button', { name: 'Clear filters' });
    fireEvent.click(clearButton);
    
    expect(mockClearFilters).toHaveBeenCalledTimes(1);
  });

  it('renders search state correctly', () => {
    render(<EmptyState type="search" context="pos" />);

    expect(screen.getByText('No results found for your search.')).toBeInTheDocument();
    expect(screen.getByText('Check spelling or try a different value')).toBeInTheDocument();
  });

  it('renders error state correctly', () => {
    render(<EmptyState type="error" context="payments" />);

    expect(screen.getByText('Unable to load transactions.')).toBeInTheDocument();
    expect(screen.getByText('Retry')).toBeInTheDocument();
  });
});