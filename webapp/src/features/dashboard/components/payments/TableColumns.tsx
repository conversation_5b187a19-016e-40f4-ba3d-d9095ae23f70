import { Badge } from '@/components/ui/badge';
import { Payment } from '@/features/dashboard/types';
import { getPaymentStatus, getPaymentStatusColor, PAYMENT_STATUS } from '@/lib/constants/status';
import { cn } from '@/lib/utils';
import { ColumnDef } from '@tanstack/react-table';
import { FilterFn } from '@tanstack/react-table';
import { format } from 'date-fns';
import { safeRender } from '@/lib/utils/stringUtils';

const multiColumnFilterFn: FilterFn<Payment> = (row, _, filterValue) => {
  const searchableRowContent = `${row.original.address} ${row.original.payment_method} ${row.original.amount} ${row.original.amount_fiat}`;

  return searchableRowContent.toLowerCase().includes(filterValue.toLowerCase().trim());
};

export const PaymentColumns: ColumnDef<Payment>[] = [
  {
    accessorKey: 'coin',
    header: 'NET?',
    cell: ({ row }) => {
      return (
        <img
          src={`https://content.ivendpay.com/pic/wefi/${row.original?.coin?.coin_id}.png`}
          className="w-9 h-9"
          alt={row.original?.coin?.name_short}
          role="img"
        />
      );
    },
  },
  {
    accessorKey: 'date',
    header: 'Date/Time',
    cell: ({ row }) => {
      const date = row.original?.processed_at
        ? new Date(row.original.processed_at)
        : row.original.date;
      return (
        <div>
          <p>{format(date, 'dd-MM-yyyy')}</p>
          <p className="text-card-foreground">{format(date, 'HH:mm a')}</p>
        </div>
      );
    },
  },
  {
    accessorKey: 'application.name',
    header: 'Type',
    cell: ({ row }) => {
      return (
        <div>
          {row.original.payment_method} {row.original.application?.name}
        </div>
      );
    },
  },
  {
    accessorKey: 'address',
    header: 'Wallet',
    cell: ({ row }) => {
      const address = row.original.address ?? '';
      return `${address.slice(0, 7)}...${address.slice(-5)}`;
    },
    filterFn: multiColumnFilterFn,
  },
  {
    accessorKey: 'amount',
    header: () => <div className="text-right">Amount</div>,
    cell: ({ row }) => {
      const amount_fiat: number = row.original?.amount_fiat ?? 0;
      const currency_fiat: string = row.original?.currency_fiat ?? 'USD';
      // TODO: change to use the user's locale
      const formatted_fiat = new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: currency_fiat,
      }).format(amount_fiat);

      const amount_crypto: number = row.original?.amount ?? 0;
      const currency_crypto: string = row.original?.coin?.name_short ?? 'USDT';

      return (
        <div className="font-medium text-right">
          <p className="text-foreground">
            {amount_crypto} {currency_crypto}
          </p>
          <p className="text-card-foreground">{formatted_fiat}</p>
        </div>
      );
    },
  },
  {
    accessorKey: 'status',
    header: () => <div className="text-right">Status</div>,
    cell: ({ row }) => {
      const status = row.getValue('status') as keyof typeof PAYMENT_STATUS.text;
      return (
        <Badge
          className={cn(getPaymentStatusColor(status), 'font-space-grotesk rounded-2xs float-end')}
        >
          {safeRender(getPaymentStatus(status))}
        </Badge>
      );
    },
  },
];
