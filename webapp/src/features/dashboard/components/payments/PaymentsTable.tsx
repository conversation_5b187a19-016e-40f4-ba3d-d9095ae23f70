import { Search } from '@/assets/icons';
import { Page } from '@/components/layouts/Page';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { DataTablePagination } from '@/components/ui/table-pagination';
import {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from '@tanstack/react-table';
import { useState } from 'react';
import { EmptyState } from '../EmptyState';
import { PaymentDetailsDialog } from '@/features/dashboard/components/payments/PaymentDetailsDialog';
import { LoadingState } from '../LoadingState';
import { Payment } from '@/features/dashboard/types';

interface PaymentsTableProps<TValue> {
  columns: ColumnDef<Payment, TValue>[];
  data: Payment[];
  title: string;
  isLoading?: boolean;
  isError?: boolean;
}

export function PaymentsTable<TValue>({
  columns,
  data,
  title,
  isLoading,
  isError,
}: PaymentsTableProps<TValue>) {
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
  const [rowSelection, setRowSelection] = useState({});
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 8,
  });
  const [selectedPayment, setSelectedPayment] = useState<Partial<Payment> | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const table = useReactTable({
    data,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    onPaginationChange: setPagination,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
      pagination,
    },
  });

  const searchValue = (table.getColumn('address')?.getFilterValue() as string) ?? '';
  const hasFilters = columnFilters.length > 0;
  const hasFilteredData = table.getRowModel().rows?.length > 0;

  const handleClearFilters = () => {
    setColumnFilters([]);
    table.resetColumnFilters();
  };

  if (isLoading) {
    return (
      <Page title={title}>
        <LoadingState />
      </Page>
    );
  }

  if (isError) {
    return (
      <Page title={title}>
        <EmptyState type="error" context="payments" />
      </Page>
    );
  }

  const handleRowClick = (payment: Payment) => {
    setSelectedPayment(payment);
    setIsDialogOpen(true);
  };

  const handleDialogClose = () => {
    setIsDialogOpen(false);
    setSelectedPayment(null);
  };

  return (
    <Page title={title}>
      <div className="flex flex-col">
        <div className="flex flex-col flex-grow gap-5">
          <div className="flex justify-end">
            <div className="relative max-w-sm">
              <Input
                placeholder="Search"
                value={searchValue}
                onChange={(event) => table.getColumn('address')?.setFilterValue(event.target.value)}
                className="pl-12"
              />
              <Search className="h-4 w-4 absolute left-5 top-1/3" />
            </div>
          </div>
          <Table>
            <TableHeader>
              {table.getHeaderGroups().map((headerGroup) => (
                <TableRow key={headerGroup.id} className="border-none">
                  {headerGroup.headers.map((header) => {
                    return (
                      <TableHead
                        key={header.id}
                        className="uppercase font-normal text-sm [&>th:first-child]:w-[60px]"
                      >
                        {header.isPlaceholder
                          ? null
                          : flexRender(header.column.columnDef.header, header.getContext())}
                      </TableHead>
                    );
                  })}
                </TableRow>
              ))}
            </TableHeader>
            <TableBody>
              {hasFilteredData ? (
                table.getRowModel().rows.map((row) => (
                  <TableRow
                    key={row.id}
                    className="items-center text-popover-foreground bg-muted/50 hover:bg-foreground/15 rounded-lg"
                    onClick={() => handleRowClick(row.original)}
                  >
                    {row.getVisibleCells().map((cell) => (
                      <TableCell key={cell.id}>
                        {flexRender(cell.column.columnDef.cell, cell.getContext())}
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={columns.length} className="h-24">
                    <EmptyState
                      type={searchValue ? 'search' : hasFilters ? 'filtered' : 'new'}
                      context="payments"
                      onClearFilters={hasFilters ? handleClearFilters : undefined}
                    />
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      </div>
      {hasFilteredData && (
        <div className="mt-auto pt-4">
          <DataTablePagination table={table} />
        </div>
      )}
      {selectedPayment && isDialogOpen && (
        <PaymentDetailsDialog
          payment={selectedPayment}
          isOpen={isDialogOpen}
          onClose={handleDialogClose}
        />
      )}
    </Page>
  );
}
