import { describe, it, expect, vi } from 'vitest';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import { LastPaymentsCard } from './LastPaymentsCard.tsx';
import { Payment } from '@/features/dashboard/types';
import { formatDate } from 'date-fns';

vi.mock('@tanstack/react-router', () => ({
  Link: vi.fn(({ to, children, className }: { to: string; children: React.ReactNode; className?: string }) => (
    <a href={to} className={className} data-testid="router-link">
      {children}
    </a>
  ))
}));

const mockPayments: Payment[] = [
  {
    id: '1',
    address: '0x0b123456789ABC123456789ABC123456789ABCd',
    amount: 100,
    date: '2025-05-24 ',
    coin: {
      coin_id: 'btc',
      name_short: 'BTC'
    },
    application: {
      name: 'Test POS'
    },
    processed_at: 1711276800000,
    payment_method: 'POS',
    amount_fiat: 50,
    currency_fiat: 'USD',
    status: 'PAID'
  },
  {
    id: '2',
    address: '0x0b123456789ABC123456789ABC123456789ABCd',
    amount: 200,
    date: '2025-05-24',
    coin: {
      coin_id: 'eth',
      name_short: 'ETH'
    },
    application: {
      name: 'Another POS'
    },
    processed_at: 1711276800000,
    payment_method: 'POS',
    amount_fiat: 750,
    currency_fiat: 'USD',
    status: 'PENDING'
  }
];

describe('LastPayments', () => {
  it('renders multiple payment items correctly', () => {
    render(<LastPaymentsCard data={mockPayments} />);

    expect(screen.getByText('Test POS')).toBeInTheDocument();
    expect(screen.getByText(/100/)).toBeInTheDocument();
    expect(screen.getByText(/BTC/)).toBeInTheDocument();
    expect(screen.getByText(/\$50/)).toBeInTheDocument();
    expect(screen.getByText('Completed')).toBeInTheDocument();

    expect(screen.getByText('Another POS')).toBeInTheDocument();
    expect(screen.getByText(/200/)).toBeInTheDocument();
    expect(screen.getByText(/ETH/)).toBeInTheDocument();
    expect(screen.getByText(/\$750/)).toBeInTheDocument();
    expect(screen.getByText('Pending')).toBeInTheDocument();
  });

  it('renders empty state when no data provided', () => {
    render(<LastPaymentsCard data={[]} />);
    
    expect(screen.getByText('There are no recent payments yet.')).toBeInTheDocument();
  });

  it('handles missing optional fields gracefully', () => {
    const minimalPayment: Payment[] = [{
      id: '2',
      address: '0x0b123456789ABC123456789ABC123456789ABCd',
      amount: 50,
      date: '2025-05-24',
      status: 'PAID',
      payment_method: 'Invoice',
    }];

    render(<LastPaymentsCard data={minimalPayment} />);

    expect(screen.getByText(/50/)).toBeInTheDocument();
    expect(screen.getByText('2025-05-24')).toBeInTheDocument();
    expect(screen.getByText('Completed')).toBeInTheDocument();
  });

  it('formats dates correctly', () => {
    const paymentsWithDates: Payment[] = [{
      id: '1',
      address: '0x0b123456789ABC123456789ABC123456789ABCd',
      amount: 100,
      date: '2025-05-26',
      processed_at: 1748252816348,
      payment_method: 'POS',
      status: 'PAID'
    }];

    render(<LastPaymentsCard data={paymentsWithDates} />);
    expect(screen.getByText(formatDate(paymentsWithDates[0].processed_at!, 'yyyy-MM-dd hh:mm'))).toBeInTheDocument();
  });

  it('handles different payment statuses', () => {
    const statusPayments: Payment[] = [
      { id: '1', address: '0x0b123456789ABC123456789ABC123456789ABCd', amount: 100, date: '2025-05-24', status: 'PAID',
        payment_method: 'POS' },
      { id: '2', address: '0x0b123456789ABC123456789ABC123456789ABCd', amount: 200, date: '2025-05-24', status: 'PENDING',
        payment_method: 'Invoice' },
      { id: '3', address: '0x0b123456789ABC123456789ABC123456789ABCd', amount: 300, date: '2025-05-24', status: 'FAILED',
        payment_method: 'API' }
    ];

    render(<LastPaymentsCard data={statusPayments} />);

    expect(screen.getByText('Completed')).toBeInTheDocument();
    expect(screen.getByText('Pending')).toBeInTheDocument();
    expect(screen.getByText('Failed')).toBeInTheDocument();
  });
}); 