import { Badge } from '@/components/ui/badge';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Payment } from '@/features/dashboard/types';
import { getPaymentStatus, getPaymentStatusColor } from '@/lib/constants/status';
import { cn } from '@/lib/utils';
import { formatFiatAmount } from '@/lib/utils/currency';
import { formatDate } from 'date-fns';
import { Button } from '@/components/ui/button';
import { CopyButton } from '@/components/ui/copy-button';
import { useEffect } from 'react';
import { toast } from '@/hooks/use-toast';
import { hasIncompleteFields, safeRender } from '@/lib/utils/stringUtils';

interface PaymentDetailsDialogProps {
  payment: Partial<Payment>;
  isOpen: boolean;
  onClose: () => void;
}

export function PaymentDetailsDialog({ payment, isOpen, onClose }: PaymentDetailsDialogProps) {
  useEffect(() => {
    if (!payment || !isOpen) return;

    if (hasIncompleteFields(payment)) {
      toast({
        title: 'Data Loading Error',
        description: 'Unable to load some details. Please retry.',
        variant: 'destructive',
      });
    }
  }, [isOpen, payment]);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="xl:min-w-[600px]">
        <DialogHeader>
          <DialogTitle>Transaction Details</DialogTitle>
        </DialogHeader>
        <DialogDescription>Transaction details for #{payment.id}</DialogDescription>
        <div className="grid gap-6 py-4">
          <div className="grid w-full max-w-sm items-center gap-3">
            <Label htmlFor="status">Status</Label>
            <Badge
              className={cn(
                getPaymentStatusColor(payment.status),
                'font-space-grotesk rounded-2xs'
              )}
            >
              {safeRender(getPaymentStatus(payment.status))}
            </Badge>
          </div>
          <div className="grid grid-cols-2">
            <div className="grid w-full max-w-sm items-center gap-3">
              <Label>Created at</Label>
              <div>
                {payment.processed_at
                  ? formatDate(payment.processed_at, 'yyyy-MM-dd hh:mm')
                  : safeRender(payment.date)}
              </div>
            </div>
            <div className="grid w-full max-w-sm items-center gap-3">
              <Label>POS</Label>
              <div>{safeRender(payment.payment_method)}</div>
            </div>
          </div>
          <div className="grid grid-cols-2">
            <div className="flex flex-col w-full max-w-sm gap-3">
              <Label>Amount</Label>
              <div>
                <p className="font-medium text-primary">
                  {safeRender(payment.amount)} {safeRender(payment.coin?.name_short)}
                </p>
                <p className="text-card-foreground">
                  {safeRender(formatFiatAmount(payment.amount_fiat, payment.currency_fiat))}
                </p>
              </div>
            </div>
            <div className="flex flex-col w-full max-w-sm gap-3">
              <Label>Exchange rate</Label>
              <div>~</div>
            </div>
          </div>
          <div className="grid gap-4">
            <div className="grid w-full max-w-sm items-center gap-3">
              <Label>Transaction ID</Label>
              <div className="flex flex-row">
                <span>{safeRender(payment.address)}</span>
                {payment.address && (
                  <CopyButton variant="transparent" className="h-6" value={payment.address} />
                )}
              </div>
            </div>
          </div>
        </div>
        <div className="">
          <Button variant="ghost" size="lg">
            Refund
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
