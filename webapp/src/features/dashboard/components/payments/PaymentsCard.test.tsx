import { describe, expect, it, vi } from 'vitest';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import { PaymentsCard } from './PaymentsCard.tsx';
import type { Payment } from '../../types.ts';
import {formatDate} from "date-fns";

// Mock the router
vi.mock('@tanstack/react-router', () => ({
  Link: vi.fn(({ to, children, className }: { to: string; children: React.ReactNode; className?: string }) => (
    <a href={to} className={className} data-testid="router-link">
      {children}
    </a>
  ))
}));

// Mock the recharts components
vi.mock('recharts', () => ({
  ResponsiveContainer: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="responsive-container">{children}</div>
  ),
  BarChart: ({ children, data }: { children: React.ReactNode; data: any[] }) => (
    <div data-testid="bar-chart" data-chart-data={JSON.stringify(data)}>
      {children}
    </div>
  ),
  Bar: ({ dataKey }: { dataKey: string }) => <div data-testid={`bar-${dataKey}`} />,
  XAxis: () => <div data-testid="xaxis" />,
  YAxis: () => <div data-testid="yaxis" />
}));

describe('Payments', () => {
  const mockDate = new Date('2024-05-26');
  vi.setSystemTime(mockDate);

  const mockPayments: Payment[] = [
    {
      id: '1',
      address: '0x0b123456789ABC123456789ABC123456789ABCd',
      amount: 1500.5,
      date: formatDate(Date.now(),  'yyyy-MM-dd'),
      payment_method: 'POS',
      coin: {
        coin_id: 'usdt-bep20',
        name_short: 'USDT',
      },
      application: {
        name: 'Online Store',
      },
      processed_at: Date.now(),
      amount_fiat: 1500.5,
      currency_fiat: 'USD',
      status: 'PAID',
    },
    {
      id: '2',
      address: '0x0b123456789ABC123456789ABC123456789CBAd',
      amount: 500.0,
      date: formatDate(Date.now() - 86400000,  'yyyy-MM-dd'),
      payment_method: 'QR',
      coin: {
        coin_id: 'usdt-bep20',
        name_short: 'USDT',
      },
      application: {
        name: 'Service Payment',
      },
      processed_at: Date.now() - 86400000,
      amount_fiat: 500.0,
      currency_fiat: 'USD',
      status: 'PENDING',
    }
  ];

  it('renders empty state when no activity', () => {
    render(<PaymentsCard data={[]} />);
    expect(screen.getByText('No activity in the past week.')).toBeInTheDocument();
  });

  it('renders chart with data', () => {
    render(<PaymentsCard data={mockPayments} />);
    expect(screen.getByTestId('bar-chart')).toBeInTheDocument();
    expect(screen.getByTestId('bar-amount')).toBeInTheDocument();
    expect(screen.getByTestId('xaxis')).toBeInTheDocument();
  });

  it('calculates daily totals correctly', () => {
    const payments: Payment[] = [
      {
        ...mockPayments[0],
        amount: 100,
        date: '2024-05-26'
      },
      {
        ...mockPayments[0],
        id: '2',
        amount: 200,
        date: '2024-05-26'
      }
    ];

    const { container } = render(<PaymentsCard data={payments} />);
    const chartData = JSON.parse(container.querySelector('[data-chart-data]')?.getAttribute('data-chart-data') || '[]');
    const dayTotal = chartData.find((d: any) => d.date === '2024-05-26')?.amount;
    expect(dayTotal).toBe(300);
  });

  it('handles payments outside 7-day window', () => {
    const oldPayment: Payment = {
      ...mockPayments[0],
      date: '2024-05-01', // Outside 7-day window
    };

    render(<PaymentsCard data={[oldPayment]} />);
    expect(screen.getByText('No activity in the past week.')).toBeInTheDocument();
  });
}); 