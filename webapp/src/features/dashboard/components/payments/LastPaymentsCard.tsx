import { Badge } from '@/components/ui/badge';
import { getPaymentStatus, getPaymentStatusColor } from '@/lib/constants/status';
import { Payment } from '@/features/dashboard/types';
import { cn } from '@/lib/utils';
import { formatDate } from 'date-fns';
import { DashboardCard } from '../DashboardCard';
import { formatFiatAmount } from '@/lib/utils/currency';

interface LastPaymentsProps {
  data: Payment[];
}

export function LastPaymentsCard({ data }: LastPaymentsProps) {
  return (
    <DashboardCard
      title="Last Payments"
      route="/payments"
      isEmpty={!data.length}
      emptyMessage="There are no recent payments yet."
    >
      <div className="space-y-4">
        {data.map((payment) => (
          <div
            key={payment.id}
            className="grid grid-cols-10 xl:grid-cols-10 lg:grid-cols-11 justify-between md:py-3 md:px-4 gap-4 p-3 rounded-sm bg-muted/50"
          >
            <div className="flex items-center md:space-x-3 xl:col-span-4 lg:col-span-5 col-span-4">
              <img
                src={`https://content.ivendpay.com/pic/wefi/${payment.coin?.coin_id}.png`}
                className="w-8 h-8"
                alt={payment.coin?.name_short}
              />
              <div>
                <p className="text-popover-foreground">{payment.application?.name || 'Payment'}</p>
                <p>
                  {payment.processed_at
                    ? formatDate(payment.processed_at, 'yyyy-MM-dd hh:mm')
                    : payment.date}
                </p>
              </div>
            </div>
            <div className="text-right col-span-3">
              <p className="font-bold text-popover-foreground">
                {payment.amount} {payment.coin?.name_short}
              </p>
              {payment.amount_fiat && payment.currency_fiat && (
                <p>{formatFiatAmount(payment.amount_fiat, payment.currency_fiat)}</p>
              )}
            </div>
            <div className="text-accent content-center text-right col-span-3">
              {payment.status && (
                <Badge
                  className={cn(
                    getPaymentStatusColor(payment.status),
                    'font-space-grotesk rounded-2xs'
                  )}
                >
                  {getPaymentStatus(payment.status)}
                </Badge>
              )}
            </div>
          </div>
        ))}
      </div>
    </DashboardCard>
  );
}
