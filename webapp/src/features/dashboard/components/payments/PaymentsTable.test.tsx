import { describe, expect, it, vi } from 'vitest';
import { render, screen } from '@testing-library/react';
import { PaymentsTable } from './PaymentsTable';
import { Payment } from '@/features/dashboard/types';
import { PaymentColumns } from './TableColumns';

// Mock Page component
vi.mock('@/components/layouts/Page', () => ({
  Page: ({ title, children }: { title: string; children: React.ReactNode }) => (
    <div data-testid="page-component">
      <h1>{title}</h1>
      {children}
    </div>
  ),
}));

// Mock data
const mockPayments: Payment[] = [
  {
    id: '1',
    address: '0x0b123456789ABC123456789ABC123456789ABCd',
    amount: 1500.5,
    date: '2025-05-26',
    payment_method: 'POS',
    coin: {
      coin_id: 'usdt-bep20',
      name_short: 'USDT',
    },
    application: {
      name: 'Online Store',
    },
    processed_at: Date.now(),
    amount_fiat: 1500.5,
    currency_fiat: 'USD',
    status: 'PAID',
  },
  {
    id: '2',
    address: '0x0b123456789ABC123456789ABC123456789CBSd',
    amount: 500.0,
    date: '2025-05-25',
    payment_method: 'QR',
    coin: {
      coin_id: 'usdt-bep20',
      name_short: 'USDT',
    },
    application: {
      name: 'Service Payment',
    },
    processed_at: Date.now() - 86400000,
    amount_fiat: 500.0,
    currency_fiat: 'USD',
    status: 'PENDING',
  },
];

describe('PaymentsTable', () => {
  it('should render with Page component wrapper', () => {
    const title = "Payments";
    render(<PaymentsTable columns={PaymentColumns} data={[]} title={title} />);
    
    expect(screen.getByTestId('page-component')).toBeInTheDocument();
    expect(screen.getByText(title)).toBeInTheDocument();
  });

  it('should render table headers correctly', () => {
    render(<PaymentsTable columns={PaymentColumns} data={[]} title="Payments" />);

    expect(screen.getByRole('columnheader', { name: /NET\?/i })).toBeInTheDocument();
    expect(screen.getByRole('columnheader', { name: /Date\/Time/i })).toBeInTheDocument();
    expect(screen.getByRole('columnheader', { name: /Type/i })).toBeInTheDocument();
    expect(screen.getByRole('columnheader', { name: /Wallet/i })).toBeInTheDocument();
    expect(screen.getByRole('columnheader', { name: /Amount/i })).toBeInTheDocument();
    expect(screen.getByRole('columnheader', { name: /Status/i })).toBeInTheDocument();
  });

  it('should render empty table when no payments provided', () => {
    render(<PaymentsTable columns={PaymentColumns} data={[]} title="Payments" />);
    expect(screen.getByText('No payments yet. Once a customer pays via POS, QR code, or invoice, transactions will appear here.')).toBeInTheDocument();
  });

  it('should render all payments correctly', () => {
    render(<PaymentsTable columns={PaymentColumns} data={mockPayments} title="Payments" />);

    mockPayments.forEach((payment) => {
      const truncatedAddress = `${payment.address.slice(0, 7)}...${payment.address.slice(-5)}`;
      expect(screen.getByText(truncatedAddress)).toBeInTheDocument();
      
      expect(screen.getByText(`${payment.amount} ${payment.coin?.name_short}`)).toBeInTheDocument();
      
      const formattedFiat = new Intl.NumberFormat("en-US", {
        style: "currency",
        currency: payment.currency_fiat || 'USD',
      }).format(payment.amount_fiat || 0);
      expect(screen.getByText(formattedFiat)).toBeInTheDocument();
    });
  });
});