import { Payment } from '@/features/dashboard/types';
import { useEffect, useRef, useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>, Responsive<PERSON><PERSON>r, <PERSON>Axi<PERSON>, YAxis } from 'recharts';
import { DashboardCard } from '../DashboardCard';

interface PaymentsProps {
  data: Payment[];
}

const TICK_OFFSET_Y = 25;
const TICK_OFFSET_X = 20;
const MIN_CHART_HEIGHT = 400;

function formatDate(dateStr: string): string {
  const [, month, day] = dateStr.split('-');
  return `${day}.${month}`;
}

function CustomTick({ x, y, payload }: { x: number; y: number; payload: { value: string } }) {
  return (
    <g transform={`translate(${x + TICK_OFFSET_X},${y + TICK_OFFSET_Y})`}>
      <text x={0} y={0} dy={16} textAnchor="end" fill="var(--card-foreground)">
        {formatDate(payload.value)}
      </text>
    </g>
  );
}

function getLastSevenDays(): string[] {
  const dates: string[] = [];
  for (let i = 6; i >= 0; i--) {
    const date = new Date();
    date.setDate(date.getDate() - i);
    dates.push(date.toISOString().split('T')[0]);
  }
  return dates;
}

function prepareChartData(data: Payment[]): { date: string; amount: number }[] {
  const lastSevenDays = getLastSevenDays();
  const dailyTotals = new Map<string, number>();

  // Initialize all days with 0
  for (const date of lastSevenDays) {
    dailyTotals.set(date, 0);
  }

  // Sum up amounts for each day
  for (const payment of data) {
    if (lastSevenDays.includes(payment.date)) {
      dailyTotals.set(payment.date, (dailyTotals.get(payment.date) || 0) + payment.amount);
    }
  }

  // Convert to array format for the chart
  return lastSevenDays.map((date) => ({
    date,
    amount: dailyTotals.get(date) || 0,
  }));
}

export function PaymentsCard({ data }: PaymentsProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const [chartHeight, setChartHeight] = useState(MIN_CHART_HEIGHT);
  const chartData = prepareChartData(data);
  const hasActivity = chartData.some((day) => day.amount > 0);

  useEffect(() => {
    const updateHeight = () => {
      if (containerRef.current) {
        const height = containerRef.current.offsetHeight;
        setChartHeight(Math.min(height * 0.75, MIN_CHART_HEIGHT));
      }
    };

    updateHeight();
    window.addEventListener('resize', updateHeight);
    return () => window.removeEventListener('resize', updateHeight);
  }, []);

  const maxAmount = Math.max(...chartData.map((day) => day.amount));

  return (
    <DashboardCard
      title="Payments"
      route="/payments"
      isEmpty={!hasActivity}
      ref={containerRef}
      emptyMessage="No activity in the past week."
    >
      <div className="chart-container" style={{ height: chartHeight }}>
        <ResponsiveContainer width="100%" height="100%">
          <BarChart
            data={chartData}
            layout="horizontal"
            margin={{ top: 20, bottom: 20 }}
            barSize={20}
          >
            <XAxis
              type="category"
              dataKey="date"
              tick={(props) => <CustomTick {...props} />}
              axisLine={false}
              tickLine={false}
              interval={0}
            />
            <YAxis type="number" domain={[0, maxAmount]} hide />
            <Bar
              dataKey="amount"
              fill="#d4ef89"
              radius={6}
              label={{
                position: 'bottom',
                dy: 10,
                fill: 'var(--popover-foreground)',
                fontFamily: 'var(--font-space-grotesk)',
              }}
            />
          </BarChart>
        </ResponsiveContainer>
      </div>
    </DashboardCard>
  );
}
