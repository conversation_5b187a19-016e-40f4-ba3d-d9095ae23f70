import React from 'react';
import { describe, expect, it, vi } from 'vitest';
import { render, screen } from '@testing-library/react';
import { ActionBar } from './ActionBar';

vi.mock('@tanstack/react-router', () => ({
    Link: ({ to, children }: { to: string; children: React.ReactNode }) => (
        <a href={to}>{children}</a>
    ),
}));

describe('ActionBar', () => {
    it('renders new invoice button correctly', () => {
        render(<ActionBar />);

        const invoiceButton = screen.getByRole('link', { name: /new invoice/i });
        expect(invoiceButton).toHaveAttribute('href', '/dashboard/pos/invoicing/new');
    });

    it('renders add POS device dialog trigger', () => {
        render(<ActionBar />);

        const addDeviceButton = screen.getByRole('button', { name: /add pos device/i });
        expect(addDeviceButton).toBeInTheDocument();
    });

    it('maintains correct button styling', () => {
        render(<ActionBar />);

        const buttons = screen.getAllByRole('button');
        buttons.forEach(button => {
            expect(button).toHaveClass('gap-2', 'border', 'border-primary');
        });
    });
});