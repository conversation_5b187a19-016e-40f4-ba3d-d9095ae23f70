import { POSItem } from '../../types';
import { DashboardCard } from '../DashboardCard';

interface POSProps {
  data: POSItem[];
}

export function PosCard({ data }: POSProps) {
  return (
    <DashboardCard title="POS" route="/pos" isEmpty={!data.length}>
      <div className="flex flex-wrap justify-center gap-4">
        {data.map((item) => (
          <div
            key={item.id}
            role="listitem"
            className="flex flex-4 flex-col items-center md:py-8 md:px-9 min-w-[auto] rounded-sm p-4 bg-muted/50"
          >
            <span className="text-lg text-popover-foreground font-bold">{item.amount}</span>
            <p>{item.name}</p>
          </div>
        ))}
      </div>
    </DashboardCard>
  );
}
