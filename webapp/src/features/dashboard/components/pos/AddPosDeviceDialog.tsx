import { But<PERSON> } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { PlusIcon } from 'lucide-react';

interface AddPosDeviceDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function AddPosDeviceDialog({ open, onOpenChange }: AddPosDeviceDialogProps) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogTrigger asChild>
        <Button variant="ghost" size="lg">
          <PlusIcon /> Add POS device
        </Button>
      </DialogTrigger>
      <DialogContent aria-describedby="add-pos-device-dialog">
        <DialogHeader>
          <DialogTitle>New POS Device</DialogTitle>
        </DialogHeader>
        <DialogDescription>
          This action cannot be undone. This will permanently delete your account and remove your
          data from our servers.
        </DialogDescription>
        <div className="space-y-4">
          <p className="text-muted-foreground">
            POS device configuration form will be implemented here
          </p>
          <div className="flex justify-end gap-3">
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
            <Button
              onClick={() => {
                onOpenChange(false);
              }}
            >
              Add
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
