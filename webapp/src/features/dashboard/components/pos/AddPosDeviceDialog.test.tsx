import { describe, expect, it, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import { AddPosDeviceDialog } from './AddPosDeviceDialog';

describe('AddPosDeviceDialog', () => {
  const mockOnOpenChange = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders the trigger button correctly', () => {
    render(
      <AddPosDeviceDialog open={false} onOpenChange={mockOnOpenChange} />
    );

    const button = screen.getByRole('button', { name: /add pos device/i });
    expect(button).toBeInTheDocument();
  });

  it('shows dialog content when open', () => {
    render(
      <AddPosDeviceDialog open={true} onOpenChange={mockOnOpenChange} />
    );

    expect(screen.getByRole('dialog')).toBeInTheDocument();
    expect(screen.getByText('New POS Device')).toBeInTheDocument();
  });

  it('handles form submission', async () => {
    render(
      <AddPosDeviceDialog open={true} onOpenChange={mockOnOpenChange} />
    );

    const submitButton = screen.getByRole('button', { name: /add/i });
    fireEvent.click(submitButton);

    // Dialog should close after submission
    expect(mockOnOpenChange).toHaveBeenCalledWith(false);
  });

  it('handles dialog close', () => {
    render(
      <AddPosDeviceDialog open={true} onOpenChange={mockOnOpenChange} />
    );

    const cancelButton = screen.getByRole('button', { name: /cancel/i });
    fireEvent.click(cancelButton);

    expect(mockOnOpenChange).toHaveBeenCalledWith(false);
  });
}); 