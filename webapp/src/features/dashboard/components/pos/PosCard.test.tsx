import { describe, it, expect, vi } from 'vitest';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import { PosCard } from './PosCard';
import { POSItem } from '../../types';

vi.mock('@tanstack/react-router', () => ({
  Link: vi.fn(({ to, children, className }: { to: string; children: React.ReactNode; className?: string }) => (
    <a href={to} className={className} data-testid="router-link">
      {children}
    </a>
  ))
}));

const mockPosData: POSItem[] = [
  {
    id: '1',
    name: 'Ecommerce',
    amount: 150,
    type: "ECOMMERCE"
  },
  {
    id: '2',
    name: 'Invoices',
    amount: 250,
    type: "INVOICE"
  }
];

describe('POS', () => {
  it('renders POS items correctly', () => {
    render(<PosCard data={mockPosData} />);

    // Check first POS item
    expect(screen.getByText('Ecommerce')).toBeInTheDocument();
    expect(screen.getByText(/150/)).toBeInTheDocument();

    // Check second POS item
    expect(screen.getByText('Invoices')).toBeInTheDocument();
    expect(screen.getByText(/250/)).toBeInTheDocument();
  });

  it('renders empty state when no data provided', () => {
    render(<PosCard data={[]} />);
    expect(screen.getByText('There are no data yet.')).toBeInTheDocument();
  });

  it('renders correct number of POS items', () => {
    render(<PosCard data={mockPosData} />);
    const posItems = screen.getAllByRole('listitem');
    expect(posItems).toHaveLength(2);
  });

  it('renders POS items with correct amounts', () => {
    render(<PosCard data={mockPosData} />);
    
    mockPosData.forEach(item => {
      const amount = screen.getByText(new RegExp(item.amount.toString()));
      expect(amount).toBeInTheDocument();
    });
  });

  it('handles single POS item correctly', () => {
    const singlePosData: POSItem[] = [{
      id: '1',
      name: 'Single POS',
      amount: 100,
      type: "DEVICE"
    }];

    render(<PosCard data={singlePosData} />);
    
    expect(screen.getByText('Single POS')).toBeInTheDocument();
    expect(screen.getByText(/100/)).toBeInTheDocument();
    expect(screen.getAllByRole('listitem')).toHaveLength(1);
  });

  it('handles zero amount correctly', () => {
    const zeroAmountData: POSItem[] = [{
      id: '1',
      name: 'Zero POS',
      amount: 0,
      type: "DEVICE"
    }];

    render(<PosCard data={zeroAmountData} />);
    
    expect(screen.getByText('Zero POS')).toBeInTheDocument();
    expect(screen.getByText('0')).toBeInTheDocument();
  });
}); 