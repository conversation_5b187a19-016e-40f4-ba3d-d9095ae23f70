import { Search } from '@/assets/icons';
import { Page } from '@/components/layouts/Page';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { DataTablePagination } from '@/components/ui/table-pagination';
import { Tabs, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs';
import {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from '@tanstack/react-table';
import { useState, useMemo } from 'react';
import { EmptyState } from '../EmptyState';
import { LoadingState } from '../LoadingState';
import { AddPosDeviceDialog } from '@/features/dashboard/components/pos/AddPosDeviceDialog';
import { POSDevice } from '@/features/dashboard/types';

type TabValue = 'all' | 'paired' | 'not-paired';

interface PosDevicesTableProps<TValue> {
  columns: ColumnDef<POSDevice, TValue>[];
  data: POSDevice[];
  title: string;
  isLoading?: boolean;
  isError?: boolean;
  onRowClick?: (device: POSDevice) => void;
}

export function PosDevicesTable<TValue>({
  columns,
  data,
  title,
  isLoading,
  isError,
  onRowClick,
}: PosDevicesTableProps<TValue>) {
  const [isOpen, setIsOpen] = useState(false);
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
  const [rowSelection, setRowSelection] = useState({});
  const [activeTab, setActiveTab] = useState<TabValue>('all');

  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 8,
  });

  const filteredData = useMemo(() => {
    if (activeTab === 'all') return data;

    return data.filter((device: POSDevice) => {
      if (activeTab === 'paired') return device.status === 'PAIRED';
      if (activeTab === 'not-paired') return device.status === 'UNPAIRED';
      return true;
    });
  }, [data, activeTab]);

  const tabCounts = useMemo(() => {
    const paired = data.filter((device: POSDevice) => device.status === 'PAIRED').length;
    const unpaired = data.filter((device: POSDevice) => device.status === 'UNPAIRED').length;

    return {
      all: data.length,
      paired,
      unpaired,
    };
  }, [data]);

  const formatTabCount = (count: number) => (
    <span className="font-bold text-xs border-2 rounded-2xs px-1 w-full h-full">{count}</span>
  );

  const table = useReactTable({
    data: filteredData,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    onPaginationChange: setPagination,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
      pagination,
    },
  });

  const handleTabChange = (value: string) => {
    setActiveTab(value as TabValue);
    setPagination({ pageIndex: 0, pageSize: 8 });
  };

  const searchValue = (table.getColumn('businessName')?.getFilterValue() as string) ?? '';
  const hasFilters = columnFilters.length > 0;
  const hasFilteredData = table.getRowModel().rows?.length > 0;

  const handleClearFilters = () => {
    setColumnFilters([]);
    table.resetColumnFilters();
  };

  if (isLoading) {
    return (
      <Page title={title}>
        <LoadingState />
      </Page>
    );
  }

  if (isError) {
    return (
      <Page title={title}>
        <EmptyState type="error" context="pos" />
      </Page>
    );
  }

  return (
    <Page
      title={title}
      actions={
        <div className="[&_button]:bg-primary [&_button]:text-primary-foreground [&_button]:hover:bg-secondary [&_button]:hover:text-primary">
          <AddPosDeviceDialog open={isOpen} onOpenChange={setIsOpen} />
        </div>
      }
    >
      <Tabs value={activeTab} onValueChange={handleTabChange} className="flex flex-col gap-5">
        <div className="flex items-center justify-between">
          <TabsList className="flex flex-row gap-2">
            <TabsTrigger className="gap-2" value="all">
              All {formatTabCount(tabCounts.all)}
            </TabsTrigger>
            <TabsTrigger className="gap-2" value="paired">
              Paired {formatTabCount(tabCounts.paired)}
            </TabsTrigger>
            <TabsTrigger className="gap-2" value="not-paired">
              Not paired {formatTabCount(tabCounts.unpaired)}
            </TabsTrigger>
          </TabsList>
          <div className="flex justify-end">
            <div className="relative max-w-sm">
              <Input
                placeholder="Search"
                value={searchValue}
                onChange={(event) =>
                  table.getColumn('businessName')?.setFilterValue(event.target.value)
                }
                className="pl-12"
              />
              <Search className="h-4 w-4 absolute left-5 top-1/3" />
            </div>
          </div>
        </div>

        <TabsContent value={activeTab} className="mt-0">
          <div className="flex flex-col">
            <Table>
              <TableHeader>
                {table.getHeaderGroups().map((headerGroup) => (
                  <TableRow key={headerGroup.id} className="border-none">
                    {headerGroup.headers.map((header) => {
                      return (
                        <TableHead
                          key={header.id}
                          className="uppercase font-normal text-sm [&>th:first-child]:w-[60px]"
                        >
                          {header.isPlaceholder
                            ? null
                            : flexRender(header.column.columnDef.header, header.getContext())}
                        </TableHead>
                      );
                    })}
                  </TableRow>
                ))}
              </TableHeader>
              <TableBody>
                {hasFilteredData ? (
                  table.getRowModel().rows.map((row) => (
                    <TableRow
                      key={row.id}
                      className={`items-center text-popover-foreground bg-muted/50 hover:bg-foreground/15 rounded-lg ${
                        onRowClick ? 'cursor-pointer' : ''
                      }`}
                      onClick={() => onRowClick?.(row.original)}
                    >
                      {row.getVisibleCells().map((cell) => (
                        <TableCell key={cell.id}>
                          {flexRender(cell.column.columnDef.cell, cell.getContext())}
                        </TableCell>
                      ))}
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={columns.length} className="h-24">
                      <EmptyState
                        type={searchValue ? 'search' : hasFilters ? 'filtered' : 'new'}
                        context="pos"
                        onClearFilters={hasFilters ? handleClearFilters : undefined}
                      />
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
          {hasFilteredData && (
            <div className="mt-auto pt-4">
              <DataTablePagination table={table} />
            </div>
          )}
        </TabsContent>
      </Tabs>
    </Page>
  );
}
