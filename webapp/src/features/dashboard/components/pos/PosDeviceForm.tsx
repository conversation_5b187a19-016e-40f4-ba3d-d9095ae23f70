import { Button } from '@/components/ui/button';
import { useAppForm } from '@/components/forms/Form';
import { toast } from '@/hooks/use-toast';
import { POSDevice } from '../../types';
import z from 'zod/v4';

const deviceEditSchema = z.object({
  businessName: z.string().min(1, 'Business name is required'),
  address: z.string().min(1, 'Address is required'),
  country: z.string().min(1, 'Country is required'),
});

interface DeviceFormProps {
  device: POSDevice;
}

export function PosDeviceForm({ device }: DeviceFormProps) {
  const form = useAppForm({
    defaultValues: {
      businessName: device.businessName || '',
      address: device.address || '',
      country: device.country || '',
    },
    validators: {
      onChange: deviceEditSchema,
    },
    onSubmit: async (values) => {
      try {
        console.log('Saving device with values:', values);
        await new Promise((resolve) => setTimeout(resolve, 1000));
        toast({
          title: 'Data Saved Successfully',
          description: 'POS Device has been successfully updated.',
          variant: 'success',
        });
      } catch (error) {
        console.error('Failed to save device:', error);
        toast({
          title: 'Data Saving Error',
          description: 'Unable to save POS device details. Please retry.',
          variant: 'destructive',
        });
      }
    },
  });

  return (
    <div className="max-w-2xl">
      <form
        onSubmit={(e) => {
          e.preventDefault();
          e.stopPropagation();
          form.handleSubmit();
        }}
        className="flex flex-col gap-9"
      >
        <div className="space-y-4">
          <form.AppField
            name="businessName"
            children={(field) => <field.TextField label="Business Name" type="text" />}
          />

          <form.AppField
            name="address"
            children={(field) => <field.TextField label="Business Address" type="text" />}
          />

          <form.AppField
            name="country"
            children={(field) => <field.CountrySelectField label="Country" />}
          />
        </div>

        <div className="flex">
          <form.Subscribe selector={(state) => state.isSubmitting}>
            {(isSubmitting) => (
              <Button type="submit" disabled={isSubmitting} className="px-6 py-2">
                Save
              </Button>
            )}
          </form.Subscribe>
        </div>
      </form>
    </div>
  );
}
