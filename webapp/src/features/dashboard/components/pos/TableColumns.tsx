import { Badge } from '@/components/ui/badge';
import { POSDevice } from '@/features/dashboard/types';
import { getPosDeviceStatus, getPosDeviceStatusColor } from '@/lib/constants/status';
import { cn } from '@/lib/utils';
import { ColumnDef } from '@tanstack/react-table';
import { safeRender } from '@/lib/utils/stringUtils';
import { format } from 'date-fns';

export const PosDevicesColumns: ColumnDef<POSDevice>[] = [
  {
    accessorKey: 'type',
    header: 'Type',
  },
  {
    accessorKey: 'businessName',
    header: 'Business name',
  },
  {
    accessorKey: 'address',
    header: 'Address',
    cell: ({ row }) => {
      return (
        <div className="flex flex-row gap-2">
          <img
            src={`https://flagsapi.com/${row.original.country}/flat/24.png`}
            alt="country flag"
          />
          {row.original.address}
        </div>
      );
    },
  },
  {
    accessorKey: 'dateAdded',
    header: 'Date added',
    cell: ({ row }) => <div>{format(row.original.dateAdded, 'dd-MM-yyyy')}</div>,
  },
  {
    accessorKey: 'status',
    header: () => <div className="text-right">Status</div>,
    cell: ({ row }) => (
      <Badge
        className={cn(
          getPosDeviceStatusColor(row.original.status),
          'font-space-grotesk rounded-2xs float-end'
        )}
      >
        {safeRender(getPosDeviceStatus(row.original.status))}
      </Badge>
    ),
  },
];
