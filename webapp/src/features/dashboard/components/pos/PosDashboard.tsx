import { Page } from '@/components/layouts/Page';
import { Link } from '@tanstack/react-router';
import QrImage from '@/assets/images/qr.png';
import ApiImage from '@/assets/images/api.png';
import DeviceImage from '@/assets/images/device.png';
import InvoiceImage from '@/assets/images/invoice.png';
import EcommerceImage from '@/assets/images/ecommerce.png';
import { POSItem } from '@/features/dashboard/types';
import { LoadingState } from '../LoadingState';

interface POSProps {
  data: POSItem[] | null;
  title: string;
  isLoading?: boolean;
  isError?: boolean;
}

export function PosDashboard({ data, title, isLoading, isError }: POSProps) {
  // Helper to get the correct icon for each card
  const getIcon = (type: string) => {
    switch (type) {
      case 'DEVICE':
        return <img src={DeviceImage} alt="api" />;
      case 'API':
        return <img src={ApiImage} alt="api" />;
      case 'ECOMMERCE':
        return <img src={EcommerceImage} alt="api" />;
      case 'INVOICE':
        return <img src={InvoiceImage} alt="api" />;
      case 'QR':
        return <img src={QrImage} alt="api" />;
      default:
        return <div className="w-8 h-8" />;
    }
  };

  // TODO: replace by the actual links
  // Helper to get the correct link for each card
  const getLink = (type: string) => {
    switch (type) {
      case 'DEVICE':
        return '/dashboard/pos/devices';
      case 'API':
        return '/dashboard/pos';
      case 'ECOMMERCE':
        return '/dashboard/pos';
      case 'INVOICE':
        return '/dashboard/pos/invoicing';
      case 'QR':
        return '/dashboard/pos';
      default:
        return '/dashboard';
    }
  };

  // Helper to get additional details for each card
  const getDetails = (item: POSItem) => {
    switch (item.type) {
      case 'DEVICE':
        return (
          <div className="flex flex-col gap-1">
            <p>{item.paired} paired</p>
            <p>{item.unpaired} not paired</p>
          </div>
        );
      case 'API':
        return <p>{item.active} active</p>;
      case 'ECOMMERCE':
        return <p>{item.connected} active</p>;
      case 'INVOICE':
        return (
          <div className="flex flex-col gap-1">
            <p>{item.pending} pending payment</p>
            <p>{item.total} total</p>
          </div>
        );
      case 'QR':
        return <p>{item.total} total</p>;
      default:
        return null;
    }
  };

  if (isLoading) {
    return (
      <Page title={title}>
        <LoadingState />
      </Page>
    );
  }

  if (isError || data === null) {
    return (
      <Page title={title}>
        <div className="flex flex-col items-center justify-center py-8 text-center">
          <p className="text-lg text-foreground mb-2">Unable to load POS data.</p>
        </div>
      </Page>
    );
  }

  return (
    <Page title={title}>
      <div className="grid lg:grid-cols-2 md:grid-cols-2 grid-cols-1 auto-rows-auto gap-6">
        {data.map((item) => (
          <Link
            to={getLink(item.type)}
            key={item.id}
            className="transition-transform hover:scale-[1.02] h-full"
          >
            <div className="flex flex-row h-full items-center lg:py-6 lg:px-10 py-4 px-2 lg:gap-10 gap-4 rounded-sm bg-muted/50">
              <div className="flex-shrink-0 lg:w-fit w-16 h-16 flex items-center justify-center">
                {getIcon(item.type)}
              </div>
              <div className="flex flex-col lg:gap-4 gap-2">
                <h3 className="text-primary lg:text-3xl text-2xl font-bold leading-none">
                  {item.name}
                </h3>
                {getDetails(item)}
              </div>
            </div>
          </Link>
        ))}
      </div>
    </Page>
  );
}
