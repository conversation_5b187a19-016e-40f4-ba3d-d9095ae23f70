import { But<PERSON> } from '@/components/ui/button';
import { Link } from '@tanstack/react-router';
import { PlusIcon } from 'lucide-react';
import { useState } from 'react';
import { AddPosDeviceDialog } from './pos/AddPosDeviceDialog';

export interface Action {
  id: string;
  name: string;
  href: string;
  icon?: React.ReactNode;
}

export function ActionBar() {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="flex items-center justify-center gap-6">
      <Button
        key="new-invoice"
        variant="ghost"
        size="lg"
        asChild
        className="gap-2 border border-primary"
      >
        <Link to="/dashboard/pos/invoicing/new">
          <PlusIcon /> New invoice
        </Link>
      </Button>
      <AddPosDeviceDialog open={isOpen} onOpenChange={setIsOpen} />
    </div>
  );
}
