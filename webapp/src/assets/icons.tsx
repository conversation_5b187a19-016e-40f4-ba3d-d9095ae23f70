export const Gearwheel = ({ title = 'Gearwheel', ...props }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="17"
    height="18"
    fill="none"
    viewBox="0 0 17 18"
    {...props}
  >
    <title className="sr-only">{title}</title>
    <path
      stroke="currentColor"
      strokeWidth="2"
      d="M10.015 2.625A3.75 3.75 0 0 0 13.263 4.5c1.347 0 2.189 1.458 1.515 2.625a3.75 3.75 0 0 0 0 3.75c.674 1.167-.168 2.625-1.515 2.625a3.75 3.75 0 0 0-3.248 1.875c-.673 1.167-2.357 1.167-3.03 0A3.75 3.75 0 0 0 3.736 13.5c-1.348 0-2.19-1.458-1.516-2.625a3.75 3.75 0 0 0 0-3.75C1.547 5.958 2.389 4.5 3.737 4.5a3.75 3.75 0 0 0 3.247-1.875c.674-1.167 2.358-1.167 3.031 0Z"
    />
    <path
      stroke="currentColor"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth="2"
      d="M7 8.95a1.5 1.5 0 1 0 3 .1 1.5 1.5 0 0 0-3-.1"
    />
  </svg>
);

export const ChevronUp = ({ title = 'Chevron Up', ...props }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="11"
    height="6"
    fill="none"
    viewBox="0 0 11 6"
    {...props}
  >
    <title className="sr-only">{title}</title>
    <path
      stroke="currentColor"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth="2"
      d="m9.5 5-4-4-4 4"
    />
  </svg>
);

export const ChevronDown = ({ title = 'Chevron down', ...props }) => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <title className="sr-only">{title}</title>
    <path
      d="m6 9 6 6 6-6"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const ChevronUpSolid = ({ title = 'Chevron Up Solid', ...props }) => (
  <svg
    width="7"
    height="6"
    viewBox="0 0 7 6"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <title className="sr-only">{title}</title>
    <path d="M0.944336 5.3002L3.49341 0.700195L6.05545 5.3002H0.944336Z" fill="currentColor" />
  </svg>
);

export const House = ({ title = 'House', ...props }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="25"
    height="24"
    viewBox="0 0 25 24"
    fill="none"
    {...props}
  >
    <title className="sr-only">{title}</title>
    <path
      d="M5.75 16.88V10.9146C5.75 10.3375 6.01642 9.79278 6.47193 9.4385L11.3519 5.64294C12.0272 5.11773 12.9728 5.11773 13.6481 5.64294L18.5281 9.4385C18.9836 9.79278 19.25 10.3375 19.25 10.9146V16.88C19.25 17.9128 18.4128 18.75 17.38 18.75H15.25C14.9739 18.75 14.75 18.5261 14.75 18.25V15.6487C14.75 15.3855 14.707 15.1193 14.5677 14.896C14.3854 14.6039 14.1423 14.3523 13.853 14.1595C13.4525 13.8925 12.9817 13.75 12.5 13.75C12.0183 13.75 11.5475 13.8925 11.147 14.1595C10.8577 14.3523 10.6146 14.6039 10.4323 14.896C10.293 15.1193 10.25 15.3855 10.25 15.6487V18.25C10.25 18.5261 10.0261 18.75 9.75 18.75H7.62C6.58723 18.75 5.75 17.9128 5.75 16.88Z"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const HouseFill = ({ title = 'House Fill', ...props }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="25"
    height="24"
    viewBox="0 0 25 24"
    fill="none"
    {...props}
  >
    <title className="sr-only">{title}</title>
    <path
      d="M14.1085 5.05092C13.1624 4.31506 11.8376 4.31506 10.8915 5.05092L6.01147 8.84648C5.37328 9.34285 5 10.1061 5 10.9146V16.88C5 18.327 6.17301 19.5 7.62 19.5H9.75C10.4404 19.5 11 18.9403 11 18.25V15.6487C11 15.4554 11.0337 15.349 11.0687 15.2929C11.1946 15.091 11.3628 14.917 11.563 14.7835C11.8402 14.5987 12.1663 14.5 12.5 14.5C12.8337 14.5 13.1598 14.5987 13.437 14.7835C13.6372 14.917 13.8054 15.091 13.9313 15.2929C13.9663 15.349 14 15.4554 14 15.6487V18.25C14 18.9403 14.5596 19.5 15.25 19.5H17.38C18.827 19.5 20 18.327 20 16.88V10.9146C20 10.1061 19.6267 9.34285 18.9885 8.84648L14.1085 5.05092Z"
      fill="currentColor"
    />
  </svg>
);

export const Card = ({ title = 'Card', ...props }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="25"
    height="24"
    viewBox="0 0 25 24"
    fill="none"
    {...props}
  >
    <title className="sr-only">{title}</title>
    <path
      d="M4.25 16.75V7.25C4.25 6.14543 5.14543 5.25 6.25 5.25H18.75C19.8546 5.25 20.75 6.14543 20.75 7.25V16.75C20.75 17.8546 19.8546 18.75 18.75 18.75H6.25C5.14543 18.75 4.25 17.8546 4.25 16.75Z"
      stroke="currentColor"
      strokeWidth="2"
    />
    <path
      d="M4.25 9.25H20.75"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M13.75 15.75L17.75 15.75"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const CardFill = ({ title = 'Card Fill', ...props }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="25"
    height="24"
    viewBox="0 0 25 24"
    fill="none"
    {...props}
  >
    <title className="sr-only">{title}</title>
    <path
      d="M6.5 4.25C4.98122 4.25 3.75 5.48122 3.75 7V7.75C3.75 8.02614 3.97386 8.25 4.25 8.25H21.25C21.5261 8.25 21.75 8.02614 21.75 7.75V7C21.75 5.48122 20.5188 4.25 19 4.25H6.5Z"
      fill="currentColor"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M3.75 16.5V10.25C3.75 9.97386 3.97386 9.75 4.25 9.75H21.25C21.5261 9.75 21.75 9.97386 21.75 10.25V16.5C21.75 18.0188 20.5188 19.25 19 19.25H6.5C4.98122 19.25 3.75 18.0188 3.75 16.5ZM13.75 15C13.3358 15 13 15.3358 13 15.75C13 16.1642 13.3358 16.5 13.75 16.5H17.75C18.1642 16.5 18.5 16.1642 18.5 15.75C18.5 15.3358 18.1642 15 17.75 15H13.75Z"
      fill="currentColor"
    />
  </svg>
);

export const Settings = ({ title = 'Settings', ...props }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="25"
    height="24"
    viewBox="0 0 25 24"
    fill="none"
    {...props}
  >
    <title className="sr-only">{title}</title>
    <path
      d="M14.0152 5.625C14.6851 6.78525 15.923 7.5 17.2628 7.5C18.6099 7.5 19.4519 8.95836 18.7783 10.125C18.1085 11.2853 18.1085 12.7148 18.7783 13.875C19.4519 15.0417 18.61 16.5 17.2628 16.5C15.9231 16.5 14.6851 17.2147 14.0152 18.375C13.3417 19.5417 11.6577 19.5417 10.9841 18.375C10.3143 17.2147 9.07629 16.5 7.73654 16.5C6.38938 16.5 5.5474 15.0416 6.22098 13.875C6.89085 12.7147 6.89086 11.2852 6.22099 10.125C5.54742 8.95831 6.38938 7.5 7.73651 7.5C9.07625 7.5 10.3142 6.78525 10.9841 5.62501C11.6577 4.45833 13.3416 4.45833 14.0152 5.625Z"
      stroke="currentColor"
      strokeWidth="2"
    />
    <path
      d="M11.0008 11.9504C10.9943 12.1473 11.0266 12.3435 11.096 12.5279C11.1653 12.7123 11.2702 12.8812 11.4048 13.025C11.5395 13.1688 11.7011 13.2847 11.8805 13.3661C12.0599 13.4474 12.2535 13.4927 12.4504 13.4992C12.6473 13.5057 12.8435 13.4734 13.0279 13.404C13.2123 13.3347 13.3812 13.2298 13.525 13.0952C13.6688 12.9605 13.7847 12.7989 13.8661 12.6195C13.9474 12.4401 13.9927 12.2465 13.9992 12.0496C14.0057 11.8527 13.9734 11.6565 13.904 11.4721C13.8347 11.2877 13.7298 11.1188 13.5952 10.975C13.4605 10.8312 13.2989 10.7153 13.1195 10.6339C12.9401 10.5526 12.7465 10.5073 12.5496 10.5008C12.3527 10.4943 12.1565 10.5266 11.9721 10.596C11.7877 10.6653 11.6188 10.7702 11.475 10.9048C11.3312 11.0395 11.2153 11.2011 11.1339 11.3805C11.0526 11.5599 11.0073 11.7535 11.0008 11.9504Z"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const SettingsFill = ({ title = 'Settings Fill', ...props }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="25"
    height="24"
    viewBox="0 0 25 24"
    fill="none"
    {...props}
  >
    <title className="sr-only">{title}</title>
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M17.2628 6.5C16.2803 6.5 15.3725 5.97585 14.8812 5.125C13.8227 3.29166 11.1765 3.29167 10.1181 5.12501C9.62683 5.97585 8.71898 6.5 7.73651 6.5C5.61958 6.5 4.29649 8.79165 5.35496 10.625C5.8462 11.4758 5.84619 12.5241 5.35496 13.375C4.29648 15.2083 5.61958 17.5 7.73654 17.5C8.71902 17.5 9.62687 18.0241 10.1181 18.875C11.1766 20.7083 13.8228 20.7083 14.8813 18.875C15.3725 18.0241 16.2803 17.5 17.2628 17.5C19.3798 17.5 20.7028 15.2083 19.6444 13.375C19.1531 12.5242 19.1531 11.4759 19.6444 10.625C20.7029 8.79169 19.3798 6.5 17.2628 6.5ZM10.6277 12.7039C10.5353 12.458 10.4922 12.1964 10.5008 11.9339C10.5095 11.6714 10.5698 11.4132 10.6783 11.174C10.7868 10.9348 10.9413 10.7193 11.1331 10.5398C11.3248 10.3603 11.5501 10.2204 11.7959 10.1279C12.0417 10.0355 12.3034 9.99241 12.5659 10.0011C12.8284 10.0098 13.0866 10.0701 13.3258 10.1786C13.565 10.287 13.7805 10.4416 13.96 10.6333C14.1394 10.8251 14.2794 11.0503 14.3718 11.2961C14.4642 11.542 14.5073 11.8036 14.4987 12.0661C14.49 12.3286 14.4297 12.5868 14.3212 12.826C14.2127 13.0652 14.0582 13.2807 13.8664 13.4602C13.6747 13.6397 13.4495 13.7796 13.2036 13.8721C12.9578 13.9645 12.6961 14.0076 12.4336 13.9989C12.1711 13.9902 11.9129 13.9299 11.6737 13.8214C11.4345 13.713 11.219 13.5584 11.0396 13.3667C10.8601 13.1749 10.7201 12.9497 10.6277 12.7039Z"
      fill="currentColor"
    />
  </svg>
);

export const Alert = ({ title = 'Alert', ...props }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    {...props}
  >
    <title className="sr-only">{title}</title>
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M13.3334 7.99999C13.3334 10.9455 10.9456 13.3333 8.00008 13.3333C5.05456 13.3333 2.66675 10.9455 2.66675 7.99999C2.66675 5.05447 5.05456 2.66666 8.00008 2.66666C10.9456 2.66666 13.3334 5.05447 13.3334 7.99999ZM7.33341 9.99999C7.33341 9.66666 7.56555 9.33332 8.00008 9.33332C8.43461 9.33332 8.66675 9.66666 8.66675 9.99999C8.66675 10.3809 8.33341 10.6667 8.00008 10.6667C7.66675 10.6667 7.33341 10.3891 7.33341 9.99999ZM8.00008 8.67333C7.50008 8.67333 7.33342 8.27613 7.33342 7.99999V5.66666C7.33342 5.39051 7.61223 5.00666 8.00008 5.00666C8.38794 5.00666 8.66675 5.39719 8.66675 5.67333V8.00666C8.66675 8.28281 8.50008 8.67333 8.00008 8.67333Z"
      fill="currentColor"
    />
  </svg>
);

export const LogoWefi = ({ title = 'WeFi', ...props }) => (
  <svg
    width="107"
    height="24"
    viewBox="0 0 107 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
    role="img"
  >
    <title className="sr-only">{title}</title>
    <path
      d="M1.96432 0.520996H6.80267C7.596 0.520996 8.23965 1.15885 8.23965 1.947V15.8269H14.5141C15.3074 15.8269 15.9511 15.1891 15.9511 14.4009V1.947C15.9511 1.15973 16.5938 0.520996 17.388 0.520996H22.2264C23.0197 0.520996 23.6634 1.15885 23.6634 1.947V15.8269H29.9387C30.732 15.8269 31.3757 15.1891 31.3757 14.4009V1.947C31.3757 1.15973 32.0184 0.520996 32.8127 0.520996H37.651C38.4443 0.520996 39.088 1.15885 39.088 1.947V15.2363C39.088 15.6146 38.9365 15.9772 38.6671 16.2446L31.7974 23.0618C31.528 23.3292 31.1626 23.4794 30.7813 23.4794H23.858C23.0646 23.4794 22.421 22.8416 22.421 22.0534V17.0607L16.3737 23.0618C16.1043 23.3292 15.7389 23.4794 15.3576 23.4794H8.83663C8.45537 23.4794 8.08996 23.3292 7.82053 23.0618L0.948223 16.2437C0.67879 15.9763 0.527344 15.6137 0.527344 15.2354V1.947C0.527344 1.15885 1.17011 0.520996 1.96432 0.520996Z"
      fill="currentColor"
    />
    <path
      d="M62.8236 20.3857L60.9411 14.1845L59.2321 8.45867L57.5459 14.1837L55.6396 20.3857H51.5259L46.8672 3.61621H50.7942L52.3879 10.0122L53.824 15.8001L55.615 10.0061L57.5441 3.61621H61.0617L62.9909 10.0061L64.7827 15.8657L66.2188 10.0131L67.8116 3.61621H71.5723L66.9373 20.3857H62.8236Z"
      fill="currentColor"
    />
    <path
      d="M86.9902 20.3857V3.61621H101.986V6.5818H90.8583V10.436H98.538V13.33H90.8583V20.3857H86.9902Z"
      fill="currentColor"
    />
    <path d="M106.47 7.94922H102.77V10.4334H106.47V7.94922Z" fill="currentColor" />
    <path d="M106.47 11.8115H102.77V20.385H106.47V11.8115Z" fill="currentColor" />
    <path
      d="M74.9002 14.901C74.9239 16.6975 76.2165 17.8168 78.2144 17.8168C79.6478 17.8168 80.5415 17.3266 80.9879 16.2772H85.1016C84.7257 17.3266 84.2317 18.0038 83.2913 18.7736C81.9512 19.8701 80.376 20.383 78.3315 20.383C75.8872 20.383 73.9589 19.7303 72.7606 18.5167C71.7031 17.4437 71.1387 15.9504 71.1387 14.2474C71.1387 10.3984 73.8656 7.94922 78.1906 7.94922C81.1059 7.94922 83.2913 9.02222 84.3497 11.0048C84.9608 12.125 85.1959 13.2207 85.2434 14.9001H74.9002V14.901ZM81.2706 12.6152C80.8946 11.0293 79.86 10.2359 78.1439 10.2359C76.4278 10.2359 75.3704 11.0293 75.0173 12.6152H81.2706Z"
      fill="currentColor"
    />
  </svg>
);

export const LogoSymbol = ({ title = 'Logo symbol', ...props }) => (
  <svg
    width="39"
    height="24"
    viewBox="0 0 39 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <title className="sr-only">{title}</title>
    <path
      d="M1.93562 0.520996H6.70363C7.48542 0.520996 8.11971 1.15885 8.11971 1.947V15.8269H14.3029C15.0847 15.8269 15.719 15.1891 15.719 14.4009V1.947C15.719 1.15973 16.3524 0.520996 17.1351 0.520996H21.9031C22.6849 0.520996 23.3192 1.15885 23.3192 1.947V15.8269H29.5033C30.2851 15.8269 30.9194 15.1891 30.9194 14.4009V1.947C30.9194 1.15973 31.5528 0.520996 32.3355 0.520996H37.1035C37.8853 0.520996 38.5196 1.15885 38.5196 1.947V15.2363C38.5196 15.6146 38.3703 15.9772 38.1048 16.2446L31.335 23.0618C31.0695 23.3292 30.7094 23.4794 30.3337 23.4794H23.511C22.7292 23.4794 22.0949 22.8416 22.0949 22.0534V17.0607L16.1355 23.0618C15.87 23.3292 15.5099 23.4794 15.1342 23.4794H8.70801C8.3323 23.4794 7.9722 23.3292 7.70669 23.0618L0.934292 16.2437C0.668776 15.9763 0.519531 15.6137 0.519531 15.2354V1.947C0.519531 1.15885 1.15295 0.520996 1.93562 0.520996Z"
      fill="currentColor"
    />
  </svg>
);

export const LogoWeChain = ({ title = 'WeChain', ...props }) => (
  <svg
    width="157"
    height="24"
    viewBox="0 0 157 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <title className="sr-only">{title}</title>
    <path
      d="M1.44739 0.00118512H6.32929C7.12968 0.00118512 7.77898 0.668775 7.77898 1.4917V16.0008H14.1094C14.9098 16.0008 15.5591 15.3332 15.5591 14.5103V1.4917C15.5591 0.668775 16.2084 0.00118512 17.0088 0.00118512H21.8907C22.6911 0.00118512 23.3404 0.668775 23.3404 1.4917V16.0008H29.672C30.4724 16.0008 31.1217 15.3332 31.1217 14.5103V1.4917C31.1217 0.668775 31.771 0.00118512 32.5714 0.00118512H37.4533C38.2537 0.00118512 38.903 0.668775 38.903 1.4917V15.383C38.903 15.7779 38.7508 16.1573 38.4786 16.4372L31.5473 23.5636C31.2751 23.8435 30.9072 24 30.522 24H23.5365C22.7361 24 22.0868 23.3324 22.0868 22.5095V17.2897L15.9858 23.5625C15.7137 23.8423 15.3458 23.9988 14.9606 23.9988H8.381C7.99695 23.9988 7.6279 23.8423 7.35572 23.5625L0.424414 16.436C0.152236 16.1561 0 15.7779 0 15.3818V1.49051C0 0.667588 0.649307 0 1.44969 0L1.44739 0.00118512Z"
      fill="currentColor"
    />
    <path
      d="M46.5715 3.19385H50.3445L51.9619 9.8411L53.554 16.4136L55.5383 9.8411L57.4984 3.19385H60.9287L62.8889 9.8411L64.8732 16.4884L66.4652 9.8411L68.0826 3.19385H71.6843L66.9794 20.6214H62.9372L61.0265 14.1729L59.1399 7.69977L57.2776 14.1729L55.3416 20.6214H51.2993L46.5703 3.19385H46.5715Z"
      fill="currentColor"
    />
    <path
      d="M70.7793 13.8504C70.7793 9.49406 74.0382 6.63037 78.4244 6.63037C82.8106 6.63037 85.9705 9.36899 85.9705 14V14.2489H74.2832C74.4304 16.9128 76.0466 18.4814 78.4244 18.4814C80.3351 18.4814 81.7316 17.4609 82.3205 15.6177H85.775C84.9904 18.9045 82.2469 20.9955 78.4002 20.9955C73.9657 20.9955 70.7804 18.1821 70.7804 13.8504H70.7793ZM74.4546 12.2818H82.3447C81.8305 10.29 80.3604 9.12003 78.4002 9.12003C76.4401 9.12003 74.9458 10.29 74.4557 12.2818H74.4546Z"
      fill="currentColor"
    />
    <path
      d="M102.068 10.4892C101.602 7.4514 99.3488 5.70865 96.4086 5.70865C93.0278 5.70865 90.6995 7.97388 90.6995 11.9082C90.6995 15.8426 93.0278 18.1078 96.4086 18.1078C99.3983 18.1078 101.652 16.2903 102.068 13.1788H105.793C105.229 18.0587 101.407 20.996 96.3349 20.996C90.7731 20.996 86.877 17.6099 86.877 11.9094C86.877 6.20892 90.7973 2.82275 96.3591 2.82275C101.383 2.82275 105.229 5.68527 105.792 10.4904H102.067L102.068 10.4892Z"
      fill="currentColor"
    />
    <path
      d="M107.092 3.19385H110.644V8.8955C111.771 7.52678 113.462 6.72962 115.471 6.72962C118.901 6.72962 121.204 8.84524 121.204 12.132V20.6214H117.652V12.7048C117.652 10.4886 116.255 9.34434 114.246 9.34434C112.726 9.34434 111.403 10.041 110.644 11.1116V20.6226H107.092V3.19502V3.19385Z"
      fill="currentColor"
    />
    <path
      d="M122.307 17.0871C122.307 13.9757 126.031 13.3527 129.314 12.6303C131.495 12.1569 132.769 11.7338 132.769 10.7379C132.769 9.74208 131.739 9.11908 129.853 9.11908C127.745 9.11908 126.373 10.1652 125.957 11.7829H122.624C123.212 8.79531 126.005 6.67969 129.951 6.67969C133.896 6.67969 136.297 8.44699 136.297 11.7829V20.6206H132.793V18.8533C131.518 20.1974 129.681 20.8952 127.55 20.8952C124.315 20.8952 122.307 19.3769 122.307 17.086V17.0871ZM128.408 18.63C130.025 18.63 131.789 18.0082 132.794 16.9118V13.6753C132.059 14.2235 130.883 14.4724 129.585 14.7705C127.527 15.2439 125.788 15.5922 125.788 16.9866C125.788 18.0327 126.768 18.63 128.409 18.63H128.408Z"
      fill="currentColor"
    />
    <path
      d="M137.816 3.09473H141.369V5.85789H137.816V3.09473ZM137.816 7.02908H141.369V20.6228H137.816V7.02908Z"
      fill="currentColor"
    />
    <path
      d="M142.889 7.02872H146.441V8.89537C147.568 7.52665 149.259 6.72949 151.292 6.72949C154.698 6.72949 157.001 8.84511 157.001 12.1319V20.6213H153.449V12.7047C153.449 10.4885 152.052 9.34421 150.043 9.34421C148.523 9.34421 147.2 10.0408 146.441 11.1115V20.6225H142.889V7.02872Z"
      fill="currentColor"
    />
  </svg>
);

export const LogoITO = ({ title = 'ITO', ...props }) => (
  <svg
    width="201"
    height="24"
    viewBox="0 0 201 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <title className="sr-only">{title}</title>
    <path
      d="M4.90124 0.520508H9.74421C10.5383 0.520508 11.1826 1.15836 11.1826 1.94651V15.8264H17.463C18.2571 15.8264 18.9013 15.1886 18.9013 14.4004V1.94651C18.9013 1.15924 19.5447 0.520508 20.3397 0.520508H25.1827C25.9768 0.520508 26.621 1.15836 26.621 1.94651V15.8264H32.9023C33.6964 15.8264 34.3407 15.1886 34.3407 14.4004V1.94651C34.3407 1.15924 34.9841 0.520508 35.779 0.520508H40.622C41.4161 0.520508 42.0603 1.15836 42.0603 1.94651V15.2358C42.0603 15.6141 41.9088 15.9767 41.6391 16.2441L34.7628 23.0613C34.4932 23.3287 34.1274 23.479 33.7458 23.479H26.8158C26.0217 23.479 25.3774 22.8411 25.3774 22.053V17.0602L19.3244 23.0613C19.0547 23.3287 18.6889 23.479 18.3073 23.479H11.7801C11.3985 23.479 11.0327 23.3287 10.763 23.0613L3.88417 16.2432C3.61448 15.9759 3.46289 15.6132 3.46289 15.2349V1.94651C3.46289 1.15836 4.10627 0.520508 4.90124 0.520508Z"
      fill="currentColor"
    />
    <path
      d="M65.8193 20.3852L63.935 14.184L62.2243 8.45819L60.5366 14.1832L58.6285 20.3852H54.5108L49.8477 3.61572H53.7784L55.3737 10.0118L56.8111 15.7996L58.6038 10.0056L60.5348 3.61572H64.0558L65.9868 10.0056L67.7803 15.8652L69.2178 10.0126L70.8121 3.61572H74.5763L69.937 20.3852H65.8193Z"
      fill="currentColor"
    />
    <path
      d="M90.0078 20.3852V3.61572H105.018V6.58132H93.8795V10.4355H101.567V13.3295H93.8795V20.3852H90.0078Z"
      fill="currentColor"
    />
    <path d="M109.507 7.94873H105.803V10.4329H109.507V7.94873Z" fill="currentColor" />
    <path d="M109.507 11.811H105.803V20.3845H109.507V11.811Z" fill="currentColor" />
    <path
      d="M77.9077 14.9005C77.9315 16.697 79.2253 17.8163 81.225 17.8163C82.6599 17.8163 83.5544 17.3261 84.0013 16.2767H88.1189C87.7426 17.3261 87.2481 18.0033 86.3068 18.7731C84.9654 19.8696 83.3887 20.3826 81.3423 20.3826C78.8957 20.3826 76.9655 19.7298 75.766 18.5162C74.7075 17.4432 74.1426 15.9499 74.1426 14.2469C74.1426 10.3979 76.8721 7.94873 81.2012 7.94873C84.1194 7.94873 86.3068 9.02173 87.3662 11.0043C87.9779 12.1245 88.2132 13.2202 88.2608 14.8996H77.9077V14.9005ZM84.2842 12.6147C83.9078 11.0288 82.8723 10.2354 81.1545 10.2354C79.4368 10.2354 78.3783 11.0288 78.0249 12.6147H84.2842Z"
      fill="currentColor"
    />
    <path d="M115.574 10.6262V4.57031H116.4V10.6262H115.574Z" fill="currentColor" />
    <path
      d="M118.004 10.6262V6.39572H118.761V7.11377H118.898C119.003 6.91191 119.18 6.73023 119.429 6.56874C119.685 6.40149 120.059 6.31786 120.55 6.31786C120.911 6.31786 121.232 6.38418 121.514 6.51684C121.802 6.64949 122.032 6.84558 122.202 7.10512C122.373 7.36466 122.458 7.68475 122.458 8.06541V10.6262H121.681V8.11731C121.681 7.69629 121.56 7.39061 121.317 7.20028C121.081 7.00996 120.76 6.91479 120.354 6.91479C119.888 6.91479 119.508 7.04745 119.213 7.31275C118.925 7.57805 118.78 7.97313 118.78 8.49797V10.6262H118.004Z"
      fill="currentColor"
    />
    <path
      d="M124.034 10.6262V6.39572H124.811V10.6262H124.034ZM124.427 5.73823C124.257 5.73823 124.113 5.6892 123.995 5.59116C123.883 5.49311 123.828 5.36911 123.828 5.21915C123.828 5.06343 123.883 4.93655 123.995 4.8385C124.113 4.74045 124.257 4.69143 124.427 4.69143C124.598 4.69143 124.739 4.74045 124.85 4.8385C124.962 4.93655 125.017 5.06343 125.017 5.21915C125.017 5.36911 124.962 5.49311 124.85 5.59116C124.739 5.6892 124.598 5.73823 124.427 5.73823Z"
      fill="currentColor"
    />
    <path
      d="M128.214 10.6262C127.939 10.6262 127.726 10.5598 127.575 10.4272C127.431 10.2945 127.359 10.1129 127.359 9.88216V6.99265H125.913V6.39572H127.359V4.89041H128.135V6.39572H129.709V6.99265H128.135V9.77835C128.135 9.95137 128.23 10.0379 128.421 10.0379H129.492V10.6262H128.214Z"
      fill="currentColor"
    />
    <path
      d="M130.967 10.6262V6.39572H131.744V10.6262H130.967ZM131.36 5.73823C131.19 5.73823 131.046 5.6892 130.928 5.59116C130.816 5.49311 130.76 5.36911 130.76 5.21915C130.76 5.06343 130.816 4.93655 130.928 4.8385C131.046 4.74045 131.19 4.69143 131.36 4.69143C131.531 4.69143 131.672 4.74045 131.783 4.8385C131.894 4.93655 131.95 5.06343 131.95 5.21915C131.95 5.36911 131.894 5.49311 131.783 5.59116C131.672 5.6892 131.531 5.73823 131.36 5.73823Z"
      fill="currentColor"
    />
    <path
      d="M134.852 10.7473C134.518 10.7473 134.213 10.6954 133.938 10.5916C133.662 10.4877 133.446 10.3378 133.289 10.1417C133.131 9.9456 133.053 9.70625 133.053 9.42365C133.053 9.13527 133.131 8.89881 133.289 8.71425C133.446 8.52392 133.662 8.38262 133.938 8.29034C134.213 8.19229 134.521 8.14327 134.862 8.14327H136.484V7.83182C136.484 7.53191 136.383 7.29545 136.179 7.12242C135.983 6.9494 135.688 6.86289 135.295 6.86289C134.908 6.86289 134.606 6.94651 134.39 7.11377C134.174 7.27526 134.026 7.49442 133.947 7.77126L133.22 7.56363C133.298 7.32717 133.423 7.11377 133.593 6.92344C133.764 6.72735 133.99 6.57163 134.272 6.45628C134.56 6.33516 134.904 6.2746 135.304 6.2746C135.921 6.2746 136.399 6.41591 136.74 6.69851C137.081 6.97535 137.251 7.36754 137.251 7.87508V9.787C137.251 9.96002 137.343 10.0465 137.527 10.0465H137.969V10.6262H137.291C137.074 10.6262 136.901 10.5743 136.769 10.4704C136.638 10.3609 136.573 10.2167 136.573 10.0379V9.99463H136.455C136.383 10.1042 136.284 10.2196 136.16 10.3407C136.042 10.456 135.878 10.5512 135.668 10.6262C135.458 10.7069 135.186 10.7473 134.852 10.7473ZM134.941 10.159C135.399 10.159 135.77 10.0408 136.052 9.8043C136.34 9.56207 136.484 9.22467 136.484 8.79211V8.69694H134.891C134.583 8.69694 134.328 8.7575 134.124 8.87862C133.928 8.99397 133.829 9.16988 133.829 9.40634C133.829 9.64281 133.931 9.82737 134.134 9.96002C134.344 10.0927 134.613 10.159 134.941 10.159Z"
      fill="currentColor"
    />
    <path d="M139.033 10.6262V4.57031H139.81V10.6262H139.033Z" fill="currentColor" />
    <path
      d="M145.371 10.6262V5.22781H143.159V4.57031H148.409V5.22781H146.197V10.6262H145.371Z"
      fill="currentColor"
    />
    <path
      d="M150.713 10.7473C150.228 10.7473 149.802 10.6579 149.435 10.4791C149.068 10.2945 148.782 10.0379 148.579 9.70914C148.376 9.38039 148.274 8.99974 148.274 8.56718V8.46336C148.274 8.02503 148.376 7.6415 148.579 7.31275C148.782 6.984 149.064 6.73023 149.425 6.55144C149.785 6.36688 150.198 6.2746 150.664 6.2746C151.116 6.2746 151.516 6.36111 151.863 6.53414C152.211 6.7014 152.483 6.94363 152.68 7.26084C152.876 7.57805 152.975 7.95006 152.975 8.37685V8.68829H149.051C149.071 9.14392 149.238 9.50151 149.553 9.76104C149.867 10.0148 150.261 10.1417 150.733 10.1417C151.146 10.1417 151.464 10.0581 151.686 9.89081C151.909 9.72356 152.08 9.52458 152.198 9.29388L152.866 9.57937C152.768 9.75816 152.63 9.93695 152.453 10.1157C152.283 10.2945 152.057 10.4445 151.775 10.5656C151.5 10.6867 151.146 10.7473 150.713 10.7473ZM149.061 8.12596H152.188C152.162 7.73378 152.008 7.4281 151.726 7.20893C151.45 6.98977 151.096 6.88019 150.664 6.88019C150.225 6.88019 149.864 6.98977 149.582 7.20893C149.3 7.4281 149.127 7.73378 149.061 8.12596Z"
      fill="currentColor"
    />
    <path
      d="M156.542 10.7473C156.076 10.7473 155.657 10.6608 155.283 10.4877C154.916 10.309 154.624 10.0552 154.408 9.72644C154.198 9.39769 154.093 9.00839 154.093 8.55852V8.46336C154.093 8.00773 154.198 7.61843 154.408 7.29545C154.624 6.9667 154.916 6.71581 155.283 6.54279C155.657 6.364 156.076 6.2746 156.542 6.2746C157.001 6.2746 157.391 6.34958 157.712 6.49953C158.04 6.64949 158.295 6.84847 158.479 7.09647C158.669 7.34447 158.79 7.60977 158.843 7.89238L158.086 8.0308C158.053 7.82317 157.974 7.63284 157.85 7.45982C157.732 7.2868 157.564 7.14838 157.348 7.04456C157.132 6.94075 156.866 6.88884 156.552 6.88884C156.23 6.88884 155.942 6.95228 155.686 7.07917C155.437 7.20605 155.237 7.38773 155.087 7.62419C154.942 7.86066 154.87 8.14327 154.87 8.47201V8.54987C154.87 8.87862 154.942 9.16123 155.087 9.39769C155.237 9.63416 155.437 9.81583 155.686 9.94272C155.942 10.0696 156.23 10.133 156.552 10.133C157.03 10.133 157.394 10.0235 157.643 9.8043C157.899 9.58514 158.056 9.31406 158.115 8.99109L158.882 9.13816C158.81 9.415 158.679 9.67742 158.489 9.92542C158.299 10.1734 158.04 10.3724 157.712 10.5224C157.391 10.6723 157.001 10.7473 156.542 10.7473Z"
      fill="currentColor"
    />
    <path
      d="M160.206 10.6262V4.57031H160.983V7.07051H161.121C161.186 6.94363 161.284 6.8254 161.416 6.71581C161.553 6.60047 161.727 6.5053 161.937 6.43032C162.153 6.35535 162.425 6.31786 162.753 6.31786C163.113 6.31786 163.435 6.38707 163.716 6.52549C164.005 6.65814 164.234 6.85423 164.405 7.11377C164.575 7.36754 164.66 7.68475 164.66 8.06541V10.6262H163.884V8.11731C163.884 7.69629 163.762 7.39061 163.52 7.20028C163.284 7.00996 162.963 6.91479 162.556 6.91479C162.091 6.91479 161.711 7.04745 161.416 7.31275C161.127 7.57805 160.983 7.97313 160.983 8.49797V10.6262H160.206Z"
      fill="currentColor"
    />
    <path
      d="M166.236 10.6262V6.39572H166.994V7.11377H167.131C167.236 6.91191 167.413 6.73023 167.662 6.56874C167.918 6.40149 168.291 6.31786 168.783 6.31786C169.144 6.31786 169.465 6.38418 169.747 6.51684C170.035 6.64949 170.265 6.84558 170.435 7.10512C170.605 7.36466 170.691 7.68475 170.691 8.06541V10.6262H169.914V8.11731C169.914 7.69629 169.793 7.39061 169.55 7.20028C169.314 7.00996 168.993 6.91479 168.586 6.91479C168.121 6.91479 167.741 7.04745 167.446 7.31275C167.157 7.57805 167.013 7.97313 167.013 8.49797V10.6262H166.236Z"
      fill="currentColor"
    />
    <path
      d="M174.489 10.7473C174.004 10.7473 173.574 10.6579 173.201 10.4791C172.834 10.3003 172.545 10.0494 172.336 9.72644C172.132 9.39769 172.031 9.01127 172.031 8.56718V8.45471C172.031 8.01638 172.132 7.63284 172.336 7.3041C172.545 6.97535 172.834 6.72158 173.201 6.54279C173.574 6.364 174.004 6.2746 174.489 6.2746C174.974 6.2746 175.4 6.364 175.767 6.54279C176.141 6.72158 176.429 6.97535 176.632 7.3041C176.842 7.63284 176.947 8.01638 176.947 8.45471V8.56718C176.947 9.01127 176.842 9.39769 176.632 9.72644C176.429 10.0494 176.141 10.3003 175.767 10.4791C175.4 10.6579 174.974 10.7473 174.489 10.7473ZM174.489 10.133C175 10.133 175.407 9.99174 175.708 9.70914C176.016 9.42076 176.17 9.03434 176.17 8.54987V8.47201C176.17 7.98754 176.016 7.60401 175.708 7.3214C175.407 7.03303 175 6.88884 174.489 6.88884C173.984 6.88884 173.578 7.03303 173.27 7.3214C172.962 7.60401 172.808 7.98754 172.808 8.47201V8.54987C172.808 9.03434 172.962 9.42076 173.27 9.70914C173.578 9.99174 173.984 10.133 174.489 10.133Z"
      fill="currentColor"
    />
    <path d="M178.345 10.6262V4.57031H179.122V10.6262H178.345Z" fill="currentColor" />
    <path
      d="M182.977 10.7473C182.492 10.7473 182.063 10.6579 181.689 10.4791C181.322 10.3003 181.034 10.0494 180.824 9.72644C180.621 9.39769 180.519 9.01127 180.519 8.56718V8.45471C180.519 8.01638 180.621 7.63284 180.824 7.3041C181.034 6.97535 181.322 6.72158 181.689 6.54279C182.063 6.364 182.492 6.2746 182.977 6.2746C183.463 6.2746 183.889 6.364 184.256 6.54279C184.629 6.72158 184.918 6.97535 185.121 7.3041C185.331 7.63284 185.436 8.01638 185.436 8.45471V8.56718C185.436 9.01127 185.331 9.39769 185.121 9.72644C184.918 10.0494 184.629 10.3003 184.256 10.4791C183.889 10.6579 183.463 10.7473 182.977 10.7473ZM182.977 10.133C183.489 10.133 183.895 9.99174 184.197 9.70914C184.505 9.42076 184.659 9.03434 184.659 8.54987V8.47201C184.659 7.98754 184.505 7.60401 184.197 7.3214C183.895 7.03303 183.489 6.88884 182.977 6.88884C182.473 6.88884 182.066 7.03303 181.758 7.3214C181.45 7.60401 181.296 7.98754 181.296 8.47201V8.54987C181.296 9.03434 181.45 9.42076 181.758 9.70914C182.066 9.99174 182.473 10.133 182.977 10.133Z"
      fill="currentColor"
    />
    <path
      d="M186.598 8.53257V8.41145C186.598 7.96736 186.699 7.5867 186.902 7.26949C187.106 6.95228 187.381 6.70716 187.728 6.53414C188.076 6.36111 188.456 6.2746 188.869 6.2746C189.374 6.2746 189.76 6.36111 190.029 6.53414C190.304 6.7014 190.508 6.88884 190.639 7.09647H190.776V6.39572H191.514V11.6124C191.514 11.8431 191.442 12.0248 191.298 12.1574C191.153 12.2901 190.944 12.3564 190.668 12.3564H187.404V11.7508H190.462C190.658 11.7508 190.757 11.6643 190.757 11.4913V9.88216H190.619C190.54 10.009 190.429 10.133 190.285 10.2542C190.141 10.3753 189.954 10.4762 189.724 10.557C189.495 10.6319 189.21 10.6694 188.869 10.6694C188.456 10.6694 188.076 10.5829 187.728 10.4099C187.381 10.2369 187.106 9.99174 186.902 9.67453C186.699 9.35155 186.598 8.9709 186.598 8.53257ZM189.075 10.0638C189.574 10.0638 189.977 9.92542 190.285 9.64858C190.599 9.37174 190.757 8.99109 190.757 8.50662V8.43741C190.757 7.94717 190.599 7.56652 190.285 7.29545C189.977 7.01861 189.574 6.88019 189.075 6.88019C188.584 6.88019 188.177 7.01861 187.856 7.29545C187.541 7.56652 187.384 7.94717 187.384 8.43741V8.50662C187.384 8.99109 187.541 9.37174 187.856 9.64858C188.177 9.92542 188.584 10.0638 189.075 10.0638Z"
      fill="currentColor"
    />
    <path
      d="M193.722 12.3564V11.7508H196.476C196.666 11.7508 196.761 11.6643 196.761 11.4913V9.95137H196.623C196.558 10.0725 196.456 10.1936 196.318 10.3147C196.187 10.4301 196.013 10.5252 195.797 10.6002C195.581 10.6694 195.309 10.704 194.981 10.704C194.634 10.704 194.312 10.6377 194.017 10.505C193.729 10.3724 193.5 10.1763 193.329 9.91677C193.165 9.65723 193.083 9.34002 193.083 8.96513V6.39572H193.86V8.91322C193.86 9.32848 193.978 9.63128 194.214 9.8216C194.457 10.0119 194.781 10.1071 195.187 10.1071C195.646 10.1071 196.023 9.97444 196.318 9.70914C196.613 9.44383 196.761 9.04876 196.761 8.52392V6.39572H197.538V11.6124C197.538 11.8431 197.462 12.0248 197.311 12.1574C197.167 12.2901 196.954 12.3564 196.672 12.3564H193.722Z"
      fill="currentColor"
    />
    <path
      d="M118.071 21.8703C117.252 21.8703 116.596 21.6626 116.105 21.2474C115.619 20.8264 115.377 20.215 115.377 19.4133V18.0291C115.377 17.2275 115.619 16.619 116.105 16.2037C116.596 15.7827 117.252 15.5722 118.071 15.5722C118.897 15.5722 119.553 15.7827 120.038 16.2037C120.529 16.619 120.775 17.2275 120.775 18.0291V19.4133C120.775 20.215 120.529 20.8264 120.038 21.2474C119.553 21.6626 118.897 21.8703 118.071 21.8703ZM118.071 21.2214C118.668 21.2214 119.13 21.0657 119.458 20.7543C119.785 20.4371 119.949 19.9987 119.949 19.4393V18.0032C119.949 17.4437 119.785 17.0083 119.458 16.6968C119.13 16.3796 118.668 16.221 118.071 16.221C117.481 16.221 117.022 16.3796 116.695 16.6968C116.367 17.0083 116.203 17.4437 116.203 18.0032V19.4393C116.203 19.9987 116.367 20.4371 116.695 20.7543C117.022 21.0657 117.481 21.2214 118.071 21.2214Z"
      fill="currentColor"
    />
    <path
      d="M123.125 21.7492V18.1156H121.65V17.5187H123.125V16.4373C123.125 16.2066 123.197 16.0249 123.341 15.8923C123.492 15.7596 123.705 15.6933 123.98 15.6933H125.19V16.2816H124.196C124 16.2816 123.901 16.3681 123.901 16.5411V17.5187H125.111V18.1156H123.901V21.7492H123.125ZM126.291 21.7492V18.1156H125.111V17.5187H126.291V16.4373C126.291 16.2066 126.363 16.0249 126.507 15.8923C126.658 15.7596 126.871 15.6933 127.146 15.6933H128.356V16.2816H127.353C127.163 16.2816 127.068 16.3681 127.068 16.5411V17.5187H128.582V18.1156H127.068V21.7492H126.291Z"
      fill="currentColor"
    />
    <path
      d="M131.643 21.8703C131.158 21.8703 130.732 21.7809 130.364 21.6021C129.997 21.4175 129.712 21.1609 129.509 20.8321C129.306 20.5034 129.204 20.1227 129.204 19.6902V19.5864C129.204 19.148 129.306 18.7645 129.509 18.4357C129.712 18.107 129.994 17.8532 130.355 17.6744C130.715 17.4899 131.128 17.3976 131.594 17.3976C132.046 17.3976 132.446 17.4841 132.793 17.6571C133.141 17.8244 133.413 18.0666 133.609 18.3838C133.806 18.701 133.904 19.0731 133.904 19.4998V19.8113H129.981C130.001 20.2669 130.168 20.6245 130.482 20.884C130.797 21.1378 131.19 21.2647 131.662 21.2647C132.075 21.2647 132.393 21.1811 132.616 21.0138C132.839 20.8465 133.009 20.6476 133.127 20.4169L133.796 20.7024C133.698 20.8812 133.56 21.0599 133.383 21.2387C133.213 21.4175 132.986 21.5675 132.705 21.6886C132.429 21.8097 132.075 21.8703 131.643 21.8703ZM129.991 19.249H133.118C133.091 18.8568 132.937 18.5511 132.655 18.3319C132.38 18.1128 132.026 18.0032 131.594 18.0032C131.154 18.0032 130.794 18.1128 130.512 18.3319C130.23 18.5511 130.056 18.8568 129.991 19.249Z"
      fill="currentColor"
    />
    <path
      d="M135.259 21.7492V17.5187H136.016V18.0378H136.154C136.239 17.8532 136.37 17.7177 136.547 17.6312C136.724 17.5447 136.957 17.5014 137.245 17.5014H137.816V18.1243H137.196C136.849 18.1243 136.567 18.2108 136.351 18.3838C136.141 18.5511 136.036 18.8135 136.036 19.1711V21.7492H135.259Z"
      fill="currentColor"
    />
    <path
      d="M138.966 21.7492V17.5187H139.742V21.7492H138.966ZM139.359 16.8612C139.189 16.8612 139.044 16.8122 138.926 16.7142C138.815 16.6161 138.759 16.4921 138.759 16.3421C138.759 16.1864 138.815 16.0595 138.926 15.9615C139.044 15.8634 139.189 15.8144 139.359 15.8144C139.529 15.8144 139.67 15.8634 139.782 15.9615C139.893 16.0595 139.949 16.1864 139.949 16.3421C139.949 16.4921 139.893 16.6161 139.782 16.7142C139.67 16.8122 139.529 16.8612 139.359 16.8612Z"
      fill="currentColor"
    />
    <path
      d="M141.376 21.7492V17.5187H142.133V18.2368H142.271C142.376 18.0349 142.553 17.8532 142.802 17.6917C143.057 17.5245 143.431 17.4409 143.923 17.4409C144.283 17.4409 144.604 17.5072 144.886 17.6398C145.175 17.7725 145.404 17.9686 145.574 18.2281C145.745 18.4877 145.83 18.8077 145.83 19.1884V21.7492H145.053V19.2403C145.053 18.8193 144.932 18.5136 144.69 18.3233C144.454 18.133 144.132 18.0378 143.726 18.0378C143.26 18.0378 142.88 18.1704 142.585 18.4357C142.297 18.701 142.153 19.0961 142.153 19.621V21.7492H141.376Z"
      fill="currentColor"
    />
    <path
      d="M147.17 19.6556V19.5344C147.17 19.0904 147.272 18.7097 147.475 18.3925C147.678 18.0753 147.954 17.8302 148.301 17.6571C148.648 17.4841 149.029 17.3976 149.442 17.3976C149.946 17.3976 150.333 17.4841 150.602 17.6571C150.877 17.8244 151.08 18.0118 151.211 18.2195H151.349V17.5187H152.087V22.7354C152.087 22.9661 152.014 23.1478 151.87 23.2804C151.726 23.4131 151.516 23.4794 151.241 23.4794H147.976V22.8738H151.034C151.231 22.8738 151.329 22.7873 151.329 22.6143V21.0052H151.192C151.113 21.132 151.002 21.256 150.857 21.3772C150.713 21.4983 150.526 21.5992 150.297 21.68C150.068 21.7549 149.782 21.7924 149.442 21.7924C149.029 21.7924 148.648 21.7059 148.301 21.5329C147.954 21.3599 147.678 21.1147 147.475 20.7975C147.272 20.4745 147.17 20.0939 147.17 19.6556ZM149.648 21.1868C150.146 21.1868 150.549 21.0484 150.857 20.7716C151.172 20.4947 151.329 20.1141 151.329 19.6296V19.5604C151.329 19.0702 151.172 18.6895 150.857 18.4184C150.549 18.1416 150.146 18.0032 149.648 18.0032C149.156 18.0032 148.75 18.1416 148.429 18.4184C148.114 18.6895 147.957 19.0702 147.957 19.5604V19.6296C147.957 20.1141 148.114 20.4947 148.429 20.7716C148.75 21.0484 149.156 21.1868 149.648 21.1868Z"
      fill="currentColor"
    />
  </svg>
);

export const EyeOpen = ({ title = 'Eye Open', ...props }) => (
  <svg
    width="25"
    height="24"
    viewBox="0 0 25 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <title className="sr-only">{title}</title>
    <path
      d="M4.5 12.4447C7.7 5.33355 17.3 5.33355 20.5 12.4447"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M12.5002 16.0003C12.15 16.0003 11.8032 15.9314 11.4797 15.7973C11.1561 15.6633 10.8622 15.4669 10.6145 15.2193C10.3669 14.9717 10.1705 14.6777 10.0365 14.3541C9.90247 14.0306 9.8335 13.6839 9.8335 13.3337C9.8335 12.9835 9.90247 12.6367 10.0365 12.3132C10.1705 11.9896 10.3669 11.6957 10.6145 11.448C10.8622 11.2004 11.1561 11.004 11.4797 10.87C11.8032 10.736 12.15 10.667 12.5002 10.667C13.2074 10.667 13.8857 10.9479 14.3858 11.448C14.8859 11.9481 15.1668 12.6264 15.1668 13.3337C15.1668 14.0409 14.8859 14.7192 14.3858 15.2193C13.8857 15.7194 13.2074 16.0003 12.5002 16.0003Z"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const EyeClosed = ({ title = 'Eye Closed', ...props }) => (
  <svg
    width="25"
    height="24"
    viewBox="0 0 25 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <title className="sr-only">{title}</title>
    <path
      d="M4.5 10C5.26362 10.9609 6.16265 11.7965 7.16667 12.4785M7.16667 12.4785C8.248 13.2111 9.45522 13.7204 10.7222 13.9785C11.8967 14.2128 13.1033 14.2128 14.2778 13.9785C15.5448 13.7204 16.752 13.2111 17.8333 12.4785M7.16667 12.4785L5.83333 14.1538M20.5 10C19.7364 10.9609 18.8373 11.7965 17.8333 12.4785M17.8333 12.4785L19.1667 14.1538M10.7222 13.9775L10.2778 16M14.2778 13.9775L14.7222 16"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const BgRadialLarge = ({ ...props }) => (
  <svg
    width="1444"
    height="1100"
    viewBox="0 0 1444 1100"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <title className="sr-only">Bg Radial Large</title>
    <rect width="1444" height="1100" fill="url(#paint0_radial_5834_14715)" />
    <defs>
      <radialGradient
        id="paint0_radial_5834_14715"
        cx="0"
        cy="0"
        r="1"
        gradientUnits="userSpaceOnUse"
        gradientTransform="translate(722 -84.7826) rotate(90) scale(1184.78 722)"
      >
        <stop stopColor="currentColor" stopOpacity="0.4" />
        <stop offset="1" stopOpacity="0" />
      </radialGradient>
    </defs>
  </svg>
);

export const BgRadialSmall = ({ ...props }) => (
  <svg
    width="375"
    height="320"
    viewBox="0 0 375 320"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <title className="sr-only">Bg Radial Small</title>
    <rect width="375" height="320" fill="url(#paint0_radial_6238_11174)" />
    <defs>
      <radialGradient
        id="paint0_radial_6238_11174"
        cx="0"
        cy="0"
        r="1"
        gradientUnits="userSpaceOnUse"
        gradientTransform="translate(187.5 -24.664) rotate(90) scale(344.664 187.5)"
      >
        <stop stopColor="currentColor" stopOpacity="0.4" />
        <stop offset="1" stopOpacity="0" />
      </radialGradient>
    </defs>
  </svg>
);

export const ArrowChevronUp = ({ ...props }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="8"
    height="8"
    viewBox="0 0 8 8"
    fill="none"
    {...props}
  >
    <title className="sr-only">Arrow Chevron Up</title>
    <path
      d="M5.33366 4.66665L4.00033 3.33331L2.66699 4.66665"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const LockIcon = ({ ...props }) => (
  <svg
    width="12"
    height="13"
    viewBox="0 0 12 13"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <title className="sr-only">Lock icon</title>
    <path
      d="M0.791504 7.95868C0.791504 6.57797 1.91079 5.45868 3.2915 5.45868H8.70817C10.0889 5.45868 11.2082 6.57797 11.2082 7.95868V10.4587C11.2082 11.8394 10.0889 12.9587 8.70817 12.9587H3.2915C1.91079 12.9587 0.791504 11.8394 0.791504 10.4587V7.95868ZM3.2915 7.12535C2.83127 7.12535 2.45817 7.49845 2.45817 7.95868V10.4587C2.45817 10.9189 2.83127 11.292 3.2915 11.292H8.70817C9.16841 11.292 9.5415 10.9189 9.5415 10.4587V7.95868C9.5415 7.49845 9.16841 7.12535 8.70817 7.12535H3.2915Z"
      fill="currentColor"
    />
    <path
      d="M4.95817 2.54199L4.95817 6.10301H3.2915L3.2915 2.54199C3.2915 1.16127 4.41081 0.0419775 5.79153 0.0419922L6.2082 0.0419969C7.5889 0.0420116 8.70817 1.1613 8.70817 2.542V6.29197H7.0415V2.542C7.0415 2.08176 6.66841 1.70867 6.20818 1.70866L5.79151 1.70866C5.33127 1.70865 4.95817 2.08175 4.95817 2.54199Z"
      fill="currentColor"
    />
    <path
      d="M5.16696 9.18113C5.16334 9.2905 5.1813 9.39952 5.21981 9.50196C5.25833 9.60439 5.31664 9.69823 5.39142 9.77813C5.4662 9.85803 5.55599 9.92242 5.65565 9.96762C5.75532 10.0128 5.86291 10.0379 5.97228 10.0416C6.08166 10.0452 6.19067 10.0272 6.29311 9.98871C6.39554 9.95019 6.48939 9.89188 6.56929 9.8171C6.64919 9.74232 6.71357 9.65253 6.75877 9.55287C6.80397 9.4532 6.8291 9.34561 6.83271 9.23624C6.83633 9.12686 6.81837 9.01784 6.77986 8.91541C6.74135 8.81298 6.68303 8.71913 6.60825 8.63923C6.53347 8.55933 6.44369 8.49495 6.34402 8.44975C6.24436 8.40455 6.13677 8.37942 6.02739 8.3758C5.91802 8.37219 5.809 8.39015 5.70656 8.42866C5.60413 8.46717 5.51029 8.52548 5.43039 8.60027C5.35049 8.67505 5.2861 8.76483 5.2409 8.8645C5.1957 8.96416 5.17058 9.07175 5.16696 9.18113Z"
      fill="currentColor"
    />
  </svg>
);

export const CarouselDot = ({ ...props }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="6"
    height="6"
    viewBox="0 0 6 6"
    fill="none"
    {...props}
  >
    <title className="sr-only">Carousel Dot</title>
    <circle cx="3" cy="3" r="3" fill="currentColor" />
  </svg>
);

export const Bank = ({ ...props }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    {...props}
  >
    <title className="sr-only">Bank</title>
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M11.6025 4.364C11.8457 4.212 12.1543 4.212 12.3975 4.364L20.3975 9.364C20.7487 9.58353 20.8555 10.0462 20.636 10.3975C20.4164 10.7488 19.9537 10.8555 19.6025 10.636L12 5.88444L4.39748 10.636C4.04623 10.8555 3.58351 10.7488 3.36398 10.3975C3.14445 10.0462 3.25123 9.58353 3.60248 9.364L11.6025 4.364ZM9.24998 9C9.24998 8.58579 9.58576 8.25 9.99998 8.25H14C14.4142 8.25 14.75 8.58579 14.75 9C14.75 9.41421 14.4142 9.75 14 9.75H9.99998C9.58576 9.75 9.24998 9.41421 9.24998 9ZM6.99998 11.25C7.41419 11.25 7.74998 11.5858 7.74998 12V16C7.74998 16.4142 7.41419 16.75 6.99998 16.75C6.58576 16.75 6.24998 16.4142 6.24998 16V12C6.24998 11.5858 6.58576 11.25 6.99998 11.25ZM9.99998 11.25C10.4142 11.25 10.75 11.5858 10.75 12V16C10.75 16.4142 10.4142 16.75 9.99998 16.75C9.58576 16.75 9.24998 16.4142 9.24998 16V12C9.24998 11.5858 9.58576 11.25 9.99998 11.25ZM14 11.25C14.4142 11.25 14.75 11.5858 14.75 12V16C14.75 16.4142 14.4142 16.75 14 16.75C13.5858 16.75 13.25 16.4142 13.25 16V12C13.25 11.5858 13.5858 11.25 14 11.25ZM17 11.25C17.4142 11.25 17.75 11.5858 17.75 12V16C17.75 16.4142 17.4142 16.75 17 16.75C16.5858 16.75 16.25 16.4142 16.25 16V12C16.25 11.5858 16.5858 11.25 17 11.25ZM5.24998 19C5.24998 18.5858 5.58576 18.25 5.99998 18.25H18C18.4142 18.25 18.75 18.5858 18.75 19C18.75 19.4142 18.4142 19.75 18 19.75H5.99998C5.58576 19.75 5.24998 19.4142 5.24998 19Z"
      fill="currentColor"
    />
  </svg>
);

export const ScanQRCode = ({ ...props }) => (
  <svg
    width="32"
    height="32"
    viewBox="0 0 32 32"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <title className="sr-only">Scan QR Code</title>
    <path
      d="M6.6665 11.3327V8.99935C6.6665 8.38051 6.91234 7.78702 7.34992 7.34943C7.78751 6.91185 8.381 6.66602 8.99984 6.66602H11.3332M6.6665 20.666V22.9994C6.6665 23.6182 6.91234 24.2117 7.34992 24.6493C7.78751 25.0869 8.381 25.3327 8.99984 25.3327H11.3332M20.6665 6.66602H22.9998C23.6187 6.66602 24.2122 6.91185 24.6498 7.34943C25.0873 7.78702 25.3332 8.38051 25.3332 8.99935V11.3327M20.6665 25.3327H22.9998C23.6187 25.3327 24.2122 25.0869 24.6498 24.6493C25.0873 24.2117 25.3332 23.6182 25.3332 22.9994V20.666M11.3332 13.666C11.3332 13.0472 11.579 12.4537 12.0166 12.0161C12.4542 11.5785 13.0477 11.3327 13.6665 11.3327H18.3332C18.952 11.3327 19.5455 11.5785 19.9831 12.0161C20.4207 12.4537 20.6665 13.0472 20.6665 13.666V18.3327C20.6665 18.9515 20.4207 19.545 19.9831 19.9826C19.5455 20.4202 18.952 20.666 18.3332 20.666H13.6665C13.0477 20.666 12.4542 20.4202 12.0166 19.9826C11.579 19.545 11.3332 18.9515 11.3332 18.3327V13.666Z"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const CoinUSDT = ({ ...props }) => (
  <svg
    width="32"
    height="32"
    viewBox="0 0 32 32"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <title className="sr-only">USDT Coin</title>
    <path
      d="M15.9998 29.3327C23.3636 29.3327 29.3332 23.3631 29.3332 15.9993C29.3332 8.63555 23.3636 2.66602 15.9998 2.66602C8.63604 2.66602 2.6665 8.63555 2.6665 15.9993C2.6665 23.3631 8.63604 29.3327 15.9998 29.3327Z"
      fill="currentColor"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M17.6015 17.1516V17.15C17.5098 17.1566 17.0373 17.185 15.9832 17.185C15.1415 17.185 14.549 17.16 14.3407 17.15V17.1525C11.1007 17.01 8.68234 16.4458 8.68234 15.7708C8.68234 15.0966 11.1007 14.5325 14.3407 14.3875V16.5908C14.5523 16.6058 15.159 16.6416 15.9973 16.6416C17.0032 16.6416 17.5073 16.6 17.6015 16.5916V14.3891C20.8348 14.5333 23.2473 15.0975 23.2473 15.7708C23.2473 16.4458 20.8348 17.0083 17.6015 17.1516ZM17.6015 14.16V12.1883H22.1132V9.18164H9.829V12.1883H14.3407V14.1591C10.674 14.3275 7.9165 15.0541 7.9165 15.9241C7.9165 16.7941 10.674 17.52 14.3407 17.6891V24.0075H17.6015V17.6875C21.2623 17.5191 24.0132 16.7933 24.0132 15.9241C24.0132 15.055 21.2623 14.3291 17.6015 14.16Z"
      fill="#131313"
    />
  </svg>
);

/* TODO: we have too many different chevrons, consolodiate and rotate them */
export const ArrowChevronRight = ({ ...props }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    {...props}
  >
    <title className="sr-only">Arrow Chevron Right</title>
    <path
      d="M6.66699 5.33337L9.33366 8.00004L6.66699 10.6667"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const Plus = ({ ...props }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    {...props}
  >
    <title className="sr-only">Plus</title>
    <path
      d="M8 4.66683L8 11.3335"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M11.3334 8L4.66675 8"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const Google = ({ ...props }) => (
  <svg
    width="32"
    height="33"
    viewBox="0 0 32 33"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <title className="sr-only">Google</title>
    <path
      d="M29.7518 13.977C29.9184 14.9167 30.0014 15.8687 30 16.8225C30 21.0818 28.4468 24.6832 25.7439 27.1209H25.7475C23.3838 29.261 20.1346 30.5 16.2821 30.5C12.4942 30.5 8.86153 29.0251 6.18312 26.3996C3.50471 23.7742 2 20.2134 2 16.5005C2 12.7876 3.50471 9.22672 6.18312 6.60129C8.86153 3.97587 12.4942 2.50092 16.2821 2.50092C19.8275 2.46022 23.2514 3.76584 25.8368 6.1443L21.7592 10.1412C20.2853 8.76392 18.3181 8.00973 16.2821 8.04124C12.5562 8.04124 9.39097 10.5052 8.26268 13.823C7.66446 15.5616 7.66446 17.4445 8.26268 19.1831H8.26804C9.40168 22.4958 12.5616 24.9597 16.2874 24.9597C18.2119 24.9597 19.8651 24.4767 21.1469 23.6227H21.1415C21.8858 23.1394 22.5225 22.5133 23.0131 21.7821C23.5037 21.051 23.8382 20.2299 23.9962 19.3686H16.2821V13.9788H29.7518V13.977Z"
      fill="currentColor"
    />
  </svg>
);

export const TransactionEdit = ({ ...props }) => (
  <svg
    width="24"
    height="25"
    viewBox="0 0 24 25"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <title className="sr-only">Edit</title>
    <path
      d="M5.75 17.75H16.25"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M5.75 14.75V11.75L12.3969 5.57786C12.594 5.39486 12.9005 5.40053 13.0907 5.5907L14.9093 7.4093C15.0995 7.59947 15.1051 7.906 14.9221 8.10308L8.75 14.75H5.75Z"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const Trash = ({ ...props }) => (
  <svg
    width="24"
    height="25"
    viewBox="0 0 24 25"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <title className="sr-only">Remove</title>
    <g clipPath="url(#clip0_6652_42195)">
      <path
        d="M7.25 8.25V17C7.25 18.1046 8.14543 19 9.25 19H14.75C15.8546 19 16.75 18.1046 16.75 17V8.25M5.25 8.25H18.75"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
      />
      <path
        d="M9 5.5H15"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path d="M10.5 11.75V15.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" />
      <path d="M13.5 11.75V15.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" />
    </g>
    <defs>
      <clipPath id="clip0_6652_42195">
        <rect width="24" height="24" fill="currentColor" transform="translate(0 0.5)" />
      </clipPath>
    </defs>
  </svg>
);
export const Delete = ({ title = 'Delete', ...props }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="20"
    height="16"
    viewBox="0 0 20 16"
    fill="none"
    {...props}
  >
    <title className="sr-only">{title}</title>
    <path
      d="M16.7463 6.97519e-09C17.5764 -5.43467e-05 18.3751 0.317554 18.9785 0.887671C19.5819 1.45779 19.9443 2.23719 19.9913 3.066L19.9963 3.25V12.75C19.9963 13.5801 19.6787 14.3788 19.1086 14.9822C18.5385 15.5856 17.7591 15.948 16.9303 15.995L16.7463 16H8.24527C7.48313 16.0003 6.74513 15.7327 6.16027 15.244L6.00527 15.105L1.01027 10.355C0.385812 9.76111 0.02279 8.94352 0.00103681 8.08202C-0.0207164 7.22052 0.30058 6.38565 0.894272 5.761L1.01027 5.645L6.00527 0.895C6.55755 0.369927 7.27678 0.0556187 8.03727 0.00699998L8.24627 6.97519e-09H16.7473H16.7463ZM9.44227 4.397C9.29778 4.28948 9.11941 4.23755 8.93978 4.25069C8.76015 4.26384 8.59125 4.3412 8.46395 4.46862C8.33666 4.59603 8.25947 4.76501 8.24649 4.94465C8.23351 5.12429 8.28561 5.30261 8.39327 5.447L8.46627 5.53L10.9363 8L8.46627 10.47L8.39327 10.554C8.28575 10.6985 8.23382 10.8769 8.24697 11.0565C8.26012 11.2361 8.33747 11.405 8.46489 11.5323C8.59231 11.6596 8.76128 11.7368 8.94092 11.7498C9.12056 11.7628 9.29888 11.7107 9.44327 11.603L9.52627 11.53L11.9963 9.061L14.4663 11.531L14.5503 11.603C14.6948 11.7105 14.8731 11.7625 15.0528 11.7493C15.2324 11.7362 15.4013 11.6588 15.5286 11.5314C15.6559 11.404 15.7331 11.235 15.7461 11.0554C15.759 10.8757 15.7069 10.6974 15.5993 10.553L15.5263 10.47L13.0573 8L15.5273 5.53L15.5993 5.446C15.7068 5.3015 15.7587 5.12314 15.7456 4.94351C15.7324 4.76388 15.6551 4.59498 15.5277 4.46768C15.4002 4.34039 15.2313 4.26319 15.0516 4.25022C14.872 4.23724 14.6937 4.28934 14.5493 4.397L14.4663 4.47L11.9963 6.939L9.52627 4.469L9.44227 4.397Z"
      fill="currentColor"
    />
  </svg>
);

export const VerticalBar = ({ title = 'VerticalBar', ...props }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    className="ml-2"
    {...props}
  >
    <title className="sr-only">{title}</title>
    <path d="M4 4v16" />
  </svg>
);

export const Search = ({ title = 'Search', ...props }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    className="lucide lucide-search-icon lucide-search font-bold"
    {...props}
  >
    <title className="sr-only">{title}</title>
    <path d="m21 21-4.34-4.34" />
    <circle cx="11" cy="11" r="8" />
  </svg>
);

export const Ellipsis = ({ title = 'Ellipsis', ...props }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    {...props}
  >
    <title className="sr-only">{title}</title>
    <circle cx="12" cy="12" r="1" />
    <circle cx="19" cy="12" r="1" />
    <circle cx="5" cy="12" r="1" />
  </svg>
);

export const Loader = ({ title = 'Loader', ...props }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    className="animate-spin text-foreground h-10 w-10"
    {...props}
  >
    <title className="sr-only">{title}</title>
    <path d="M21 12a9 9 0 1 1-6.219-8.56" />
  </svg>
);
