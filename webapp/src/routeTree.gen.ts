/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

// Import Routes

import { Route as rootRoute } from './routes/__root'
import { Route as SignupImport } from './routes/signup'
import { Route as LoginImport } from './routes/login'
import { Route as AuthImport } from './routes/auth'
import { Route as ProtectedLayoutImport } from './routes/_protectedLayout'
import { Route as ProtectedLayoutIndexImport } from './routes/_protectedLayout/index'
import { Route as ProtectedLayoutSettingsImport } from './routes/_protectedLayout/settings'
import { Route as ProtectedLayoutPostsImport } from './routes/_protectedLayout/posts'
import { Route as ProtectedLayoutUsersIndexImport } from './routes/_protectedLayout/users/index'
import { Route as ProtectedLayoutDashboardIndexImport } from './routes/_protectedLayout/dashboard/index'
import { Route as ProtectedLayoutUsersUserIdImport } from './routes/_protectedLayout/users/$userId'
import { Route as ProtectedLayoutDashboardPosIndexImport } from './routes/_protectedLayout/dashboard/pos/index'
import { Route as ProtectedLayoutDashboardPaymentsIndexImport } from './routes/_protectedLayout/dashboard/payments/index'
import { Route as ProtectedLayoutDashboardPosInvoicingIndexImport } from './routes/_protectedLayout/dashboard/pos/invoicing/index'
import { Route as ProtectedLayoutDashboardPosDevicesIndexImport } from './routes/_protectedLayout/dashboard/pos/devices/index'
import { Route as ProtectedLayoutDashboardPosInvoicingNewImport } from './routes/_protectedLayout/dashboard/pos/invoicing/new'
import { Route as ProtectedLayoutDashboardPosDevicesDeviceIdImport } from './routes/_protectedLayout/dashboard/pos/devices/$deviceId'

// Create/Update Routes

const SignupRoute = SignupImport.update({
  id: '/signup',
  path: '/signup',
  getParentRoute: () => rootRoute,
} as any)

const LoginRoute = LoginImport.update({
  id: '/login',
  path: '/login',
  getParentRoute: () => rootRoute,
} as any)

const AuthRoute = AuthImport.update({
  id: '/auth',
  path: '/auth',
  getParentRoute: () => rootRoute,
} as any)

const ProtectedLayoutRoute = ProtectedLayoutImport.update({
  id: '/_protectedLayout',
  getParentRoute: () => rootRoute,
} as any)

const ProtectedLayoutIndexRoute = ProtectedLayoutIndexImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => ProtectedLayoutRoute,
} as any)

const ProtectedLayoutSettingsRoute = ProtectedLayoutSettingsImport.update({
  id: '/settings',
  path: '/settings',
  getParentRoute: () => ProtectedLayoutRoute,
} as any)

const ProtectedLayoutPostsRoute = ProtectedLayoutPostsImport.update({
  id: '/posts',
  path: '/posts',
  getParentRoute: () => ProtectedLayoutRoute,
} as any)

const ProtectedLayoutUsersIndexRoute = ProtectedLayoutUsersIndexImport.update({
  id: '/users/',
  path: '/users/',
  getParentRoute: () => ProtectedLayoutRoute,
} as any)

const ProtectedLayoutDashboardIndexRoute =
  ProtectedLayoutDashboardIndexImport.update({
    id: '/dashboard/',
    path: '/dashboard/',
    getParentRoute: () => ProtectedLayoutRoute,
  } as any)

const ProtectedLayoutUsersUserIdRoute = ProtectedLayoutUsersUserIdImport.update(
  {
    id: '/users/$userId',
    path: '/users/$userId',
    getParentRoute: () => ProtectedLayoutRoute,
  } as any,
)

const ProtectedLayoutDashboardPosIndexRoute =
  ProtectedLayoutDashboardPosIndexImport.update({
    id: '/dashboard/pos/',
    path: '/dashboard/pos/',
    getParentRoute: () => ProtectedLayoutRoute,
  } as any)

const ProtectedLayoutDashboardPaymentsIndexRoute =
  ProtectedLayoutDashboardPaymentsIndexImport.update({
    id: '/dashboard/payments/',
    path: '/dashboard/payments/',
    getParentRoute: () => ProtectedLayoutRoute,
  } as any)

const ProtectedLayoutDashboardPosInvoicingIndexRoute =
  ProtectedLayoutDashboardPosInvoicingIndexImport.update({
    id: '/dashboard/pos/invoicing/',
    path: '/dashboard/pos/invoicing/',
    getParentRoute: () => ProtectedLayoutRoute,
  } as any)

const ProtectedLayoutDashboardPosDevicesIndexRoute =
  ProtectedLayoutDashboardPosDevicesIndexImport.update({
    id: '/dashboard/pos/devices/',
    path: '/dashboard/pos/devices/',
    getParentRoute: () => ProtectedLayoutRoute,
  } as any)

const ProtectedLayoutDashboardPosInvoicingNewRoute =
  ProtectedLayoutDashboardPosInvoicingNewImport.update({
    id: '/dashboard/pos/invoicing/new',
    path: '/dashboard/pos/invoicing/new',
    getParentRoute: () => ProtectedLayoutRoute,
  } as any)

const ProtectedLayoutDashboardPosDevicesDeviceIdRoute =
  ProtectedLayoutDashboardPosDevicesDeviceIdImport.update({
    id: '/dashboard/pos/devices/$deviceId',
    path: '/dashboard/pos/devices/$deviceId',
    getParentRoute: () => ProtectedLayoutRoute,
  } as any)

// Populate the FileRoutesByPath interface

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/_protectedLayout': {
      id: '/_protectedLayout'
      path: ''
      fullPath: ''
      preLoaderRoute: typeof ProtectedLayoutImport
      parentRoute: typeof rootRoute
    }
    '/auth': {
      id: '/auth'
      path: '/auth'
      fullPath: '/auth'
      preLoaderRoute: typeof AuthImport
      parentRoute: typeof rootRoute
    }
    '/login': {
      id: '/login'
      path: '/login'
      fullPath: '/login'
      preLoaderRoute: typeof LoginImport
      parentRoute: typeof rootRoute
    }
    '/signup': {
      id: '/signup'
      path: '/signup'
      fullPath: '/signup'
      preLoaderRoute: typeof SignupImport
      parentRoute: typeof rootRoute
    }
    '/_protectedLayout/posts': {
      id: '/_protectedLayout/posts'
      path: '/posts'
      fullPath: '/posts'
      preLoaderRoute: typeof ProtectedLayoutPostsImport
      parentRoute: typeof ProtectedLayoutImport
    }
    '/_protectedLayout/settings': {
      id: '/_protectedLayout/settings'
      path: '/settings'
      fullPath: '/settings'
      preLoaderRoute: typeof ProtectedLayoutSettingsImport
      parentRoute: typeof ProtectedLayoutImport
    }
    '/_protectedLayout/': {
      id: '/_protectedLayout/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof ProtectedLayoutIndexImport
      parentRoute: typeof ProtectedLayoutImport
    }
    '/_protectedLayout/users/$userId': {
      id: '/_protectedLayout/users/$userId'
      path: '/users/$userId'
      fullPath: '/users/$userId'
      preLoaderRoute: typeof ProtectedLayoutUsersUserIdImport
      parentRoute: typeof ProtectedLayoutImport
    }
    '/_protectedLayout/dashboard/': {
      id: '/_protectedLayout/dashboard/'
      path: '/dashboard'
      fullPath: '/dashboard'
      preLoaderRoute: typeof ProtectedLayoutDashboardIndexImport
      parentRoute: typeof ProtectedLayoutImport
    }
    '/_protectedLayout/users/': {
      id: '/_protectedLayout/users/'
      path: '/users'
      fullPath: '/users'
      preLoaderRoute: typeof ProtectedLayoutUsersIndexImport
      parentRoute: typeof ProtectedLayoutImport
    }
    '/_protectedLayout/dashboard/payments/': {
      id: '/_protectedLayout/dashboard/payments/'
      path: '/dashboard/payments'
      fullPath: '/dashboard/payments'
      preLoaderRoute: typeof ProtectedLayoutDashboardPaymentsIndexImport
      parentRoute: typeof ProtectedLayoutImport
    }
    '/_protectedLayout/dashboard/pos/': {
      id: '/_protectedLayout/dashboard/pos/'
      path: '/dashboard/pos'
      fullPath: '/dashboard/pos'
      preLoaderRoute: typeof ProtectedLayoutDashboardPosIndexImport
      parentRoute: typeof ProtectedLayoutImport
    }
    '/_protectedLayout/dashboard/pos/devices/$deviceId': {
      id: '/_protectedLayout/dashboard/pos/devices/$deviceId'
      path: '/dashboard/pos/devices/$deviceId'
      fullPath: '/dashboard/pos/devices/$deviceId'
      preLoaderRoute: typeof ProtectedLayoutDashboardPosDevicesDeviceIdImport
      parentRoute: typeof ProtectedLayoutImport
    }
    '/_protectedLayout/dashboard/pos/invoicing/new': {
      id: '/_protectedLayout/dashboard/pos/invoicing/new'
      path: '/dashboard/pos/invoicing/new'
      fullPath: '/dashboard/pos/invoicing/new'
      preLoaderRoute: typeof ProtectedLayoutDashboardPosInvoicingNewImport
      parentRoute: typeof ProtectedLayoutImport
    }
    '/_protectedLayout/dashboard/pos/devices/': {
      id: '/_protectedLayout/dashboard/pos/devices/'
      path: '/dashboard/pos/devices'
      fullPath: '/dashboard/pos/devices'
      preLoaderRoute: typeof ProtectedLayoutDashboardPosDevicesIndexImport
      parentRoute: typeof ProtectedLayoutImport
    }
    '/_protectedLayout/dashboard/pos/invoicing/': {
      id: '/_protectedLayout/dashboard/pos/invoicing/'
      path: '/dashboard/pos/invoicing'
      fullPath: '/dashboard/pos/invoicing'
      preLoaderRoute: typeof ProtectedLayoutDashboardPosInvoicingIndexImport
      parentRoute: typeof ProtectedLayoutImport
    }
  }
}

// Create and export the route tree

interface ProtectedLayoutRouteChildren {
  ProtectedLayoutPostsRoute: typeof ProtectedLayoutPostsRoute
  ProtectedLayoutSettingsRoute: typeof ProtectedLayoutSettingsRoute
  ProtectedLayoutIndexRoute: typeof ProtectedLayoutIndexRoute
  ProtectedLayoutUsersUserIdRoute: typeof ProtectedLayoutUsersUserIdRoute
  ProtectedLayoutDashboardIndexRoute: typeof ProtectedLayoutDashboardIndexRoute
  ProtectedLayoutUsersIndexRoute: typeof ProtectedLayoutUsersIndexRoute
  ProtectedLayoutDashboardPaymentsIndexRoute: typeof ProtectedLayoutDashboardPaymentsIndexRoute
  ProtectedLayoutDashboardPosIndexRoute: typeof ProtectedLayoutDashboardPosIndexRoute
  ProtectedLayoutDashboardPosDevicesDeviceIdRoute: typeof ProtectedLayoutDashboardPosDevicesDeviceIdRoute
  ProtectedLayoutDashboardPosInvoicingNewRoute: typeof ProtectedLayoutDashboardPosInvoicingNewRoute
  ProtectedLayoutDashboardPosDevicesIndexRoute: typeof ProtectedLayoutDashboardPosDevicesIndexRoute
  ProtectedLayoutDashboardPosInvoicingIndexRoute: typeof ProtectedLayoutDashboardPosInvoicingIndexRoute
}

const ProtectedLayoutRouteChildren: ProtectedLayoutRouteChildren = {
  ProtectedLayoutPostsRoute: ProtectedLayoutPostsRoute,
  ProtectedLayoutSettingsRoute: ProtectedLayoutSettingsRoute,
  ProtectedLayoutIndexRoute: ProtectedLayoutIndexRoute,
  ProtectedLayoutUsersUserIdRoute: ProtectedLayoutUsersUserIdRoute,
  ProtectedLayoutDashboardIndexRoute: ProtectedLayoutDashboardIndexRoute,
  ProtectedLayoutUsersIndexRoute: ProtectedLayoutUsersIndexRoute,
  ProtectedLayoutDashboardPaymentsIndexRoute:
    ProtectedLayoutDashboardPaymentsIndexRoute,
  ProtectedLayoutDashboardPosIndexRoute: ProtectedLayoutDashboardPosIndexRoute,
  ProtectedLayoutDashboardPosDevicesDeviceIdRoute:
    ProtectedLayoutDashboardPosDevicesDeviceIdRoute,
  ProtectedLayoutDashboardPosInvoicingNewRoute:
    ProtectedLayoutDashboardPosInvoicingNewRoute,
  ProtectedLayoutDashboardPosDevicesIndexRoute:
    ProtectedLayoutDashboardPosDevicesIndexRoute,
  ProtectedLayoutDashboardPosInvoicingIndexRoute:
    ProtectedLayoutDashboardPosInvoicingIndexRoute,
}

const ProtectedLayoutRouteWithChildren = ProtectedLayoutRoute._addFileChildren(
  ProtectedLayoutRouteChildren,
)

export interface FileRoutesByFullPath {
  '': typeof ProtectedLayoutRouteWithChildren
  '/auth': typeof AuthRoute
  '/login': typeof LoginRoute
  '/signup': typeof SignupRoute
  '/posts': typeof ProtectedLayoutPostsRoute
  '/settings': typeof ProtectedLayoutSettingsRoute
  '/': typeof ProtectedLayoutIndexRoute
  '/users/$userId': typeof ProtectedLayoutUsersUserIdRoute
  '/dashboard': typeof ProtectedLayoutDashboardIndexRoute
  '/users': typeof ProtectedLayoutUsersIndexRoute
  '/dashboard/payments': typeof ProtectedLayoutDashboardPaymentsIndexRoute
  '/dashboard/pos': typeof ProtectedLayoutDashboardPosIndexRoute
  '/dashboard/pos/devices/$deviceId': typeof ProtectedLayoutDashboardPosDevicesDeviceIdRoute
  '/dashboard/pos/invoicing/new': typeof ProtectedLayoutDashboardPosInvoicingNewRoute
  '/dashboard/pos/devices': typeof ProtectedLayoutDashboardPosDevicesIndexRoute
  '/dashboard/pos/invoicing': typeof ProtectedLayoutDashboardPosInvoicingIndexRoute
}

export interface FileRoutesByTo {
  '/auth': typeof AuthRoute
  '/login': typeof LoginRoute
  '/signup': typeof SignupRoute
  '/posts': typeof ProtectedLayoutPostsRoute
  '/settings': typeof ProtectedLayoutSettingsRoute
  '/': typeof ProtectedLayoutIndexRoute
  '/users/$userId': typeof ProtectedLayoutUsersUserIdRoute
  '/dashboard': typeof ProtectedLayoutDashboardIndexRoute
  '/users': typeof ProtectedLayoutUsersIndexRoute
  '/dashboard/payments': typeof ProtectedLayoutDashboardPaymentsIndexRoute
  '/dashboard/pos': typeof ProtectedLayoutDashboardPosIndexRoute
  '/dashboard/pos/devices/$deviceId': typeof ProtectedLayoutDashboardPosDevicesDeviceIdRoute
  '/dashboard/pos/invoicing/new': typeof ProtectedLayoutDashboardPosInvoicingNewRoute
  '/dashboard/pos/devices': typeof ProtectedLayoutDashboardPosDevicesIndexRoute
  '/dashboard/pos/invoicing': typeof ProtectedLayoutDashboardPosInvoicingIndexRoute
}

export interface FileRoutesById {
  __root__: typeof rootRoute
  '/_protectedLayout': typeof ProtectedLayoutRouteWithChildren
  '/auth': typeof AuthRoute
  '/login': typeof LoginRoute
  '/signup': typeof SignupRoute
  '/_protectedLayout/posts': typeof ProtectedLayoutPostsRoute
  '/_protectedLayout/settings': typeof ProtectedLayoutSettingsRoute
  '/_protectedLayout/': typeof ProtectedLayoutIndexRoute
  '/_protectedLayout/users/$userId': typeof ProtectedLayoutUsersUserIdRoute
  '/_protectedLayout/dashboard/': typeof ProtectedLayoutDashboardIndexRoute
  '/_protectedLayout/users/': typeof ProtectedLayoutUsersIndexRoute
  '/_protectedLayout/dashboard/payments/': typeof ProtectedLayoutDashboardPaymentsIndexRoute
  '/_protectedLayout/dashboard/pos/': typeof ProtectedLayoutDashboardPosIndexRoute
  '/_protectedLayout/dashboard/pos/devices/$deviceId': typeof ProtectedLayoutDashboardPosDevicesDeviceIdRoute
  '/_protectedLayout/dashboard/pos/invoicing/new': typeof ProtectedLayoutDashboardPosInvoicingNewRoute
  '/_protectedLayout/dashboard/pos/devices/': typeof ProtectedLayoutDashboardPosDevicesIndexRoute
  '/_protectedLayout/dashboard/pos/invoicing/': typeof ProtectedLayoutDashboardPosInvoicingIndexRoute
}

export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | ''
    | '/auth'
    | '/login'
    | '/signup'
    | '/posts'
    | '/settings'
    | '/'
    | '/users/$userId'
    | '/dashboard'
    | '/users'
    | '/dashboard/payments'
    | '/dashboard/pos'
    | '/dashboard/pos/devices/$deviceId'
    | '/dashboard/pos/invoicing/new'
    | '/dashboard/pos/devices'
    | '/dashboard/pos/invoicing'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/auth'
    | '/login'
    | '/signup'
    | '/posts'
    | '/settings'
    | '/'
    | '/users/$userId'
    | '/dashboard'
    | '/users'
    | '/dashboard/payments'
    | '/dashboard/pos'
    | '/dashboard/pos/devices/$deviceId'
    | '/dashboard/pos/invoicing/new'
    | '/dashboard/pos/devices'
    | '/dashboard/pos/invoicing'
  id:
    | '__root__'
    | '/_protectedLayout'
    | '/auth'
    | '/login'
    | '/signup'
    | '/_protectedLayout/posts'
    | '/_protectedLayout/settings'
    | '/_protectedLayout/'
    | '/_protectedLayout/users/$userId'
    | '/_protectedLayout/dashboard/'
    | '/_protectedLayout/users/'
    | '/_protectedLayout/dashboard/payments/'
    | '/_protectedLayout/dashboard/pos/'
    | '/_protectedLayout/dashboard/pos/devices/$deviceId'
    | '/_protectedLayout/dashboard/pos/invoicing/new'
    | '/_protectedLayout/dashboard/pos/devices/'
    | '/_protectedLayout/dashboard/pos/invoicing/'
  fileRoutesById: FileRoutesById
}

export interface RootRouteChildren {
  ProtectedLayoutRoute: typeof ProtectedLayoutRouteWithChildren
  AuthRoute: typeof AuthRoute
  LoginRoute: typeof LoginRoute
  SignupRoute: typeof SignupRoute
}

const rootRouteChildren: RootRouteChildren = {
  ProtectedLayoutRoute: ProtectedLayoutRouteWithChildren,
  AuthRoute: AuthRoute,
  LoginRoute: LoginRoute,
  SignupRoute: SignupRoute,
}

export const routeTree = rootRoute
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()

/* ROUTE_MANIFEST_START
{
  "routes": {
    "__root__": {
      "filePath": "__root.tsx",
      "children": [
        "/_protectedLayout",
        "/auth",
        "/login",
        "/signup"
      ]
    },
    "/_protectedLayout": {
      "filePath": "_protectedLayout.tsx",
      "children": [
        "/_protectedLayout/posts",
        "/_protectedLayout/settings",
        "/_protectedLayout/",
        "/_protectedLayout/users/$userId",
        "/_protectedLayout/dashboard/",
        "/_protectedLayout/users/",
        "/_protectedLayout/dashboard/payments/",
        "/_protectedLayout/dashboard/pos/",
        "/_protectedLayout/dashboard/pos/devices/$deviceId",
        "/_protectedLayout/dashboard/pos/invoicing/new",
        "/_protectedLayout/dashboard/pos/devices/",
        "/_protectedLayout/dashboard/pos/invoicing/"
      ]
    },
    "/auth": {
      "filePath": "auth.tsx"
    },
    "/login": {
      "filePath": "login.tsx"
    },
    "/signup": {
      "filePath": "signup.tsx"
    },
    "/_protectedLayout/posts": {
      "filePath": "_protectedLayout/posts.tsx",
      "parent": "/_protectedLayout"
    },
    "/_protectedLayout/settings": {
      "filePath": "_protectedLayout/settings.tsx",
      "parent": "/_protectedLayout"
    },
    "/_protectedLayout/": {
      "filePath": "_protectedLayout/index.tsx",
      "parent": "/_protectedLayout"
    },
    "/_protectedLayout/users/$userId": {
      "filePath": "_protectedLayout/users/$userId.tsx",
      "parent": "/_protectedLayout"
    },
    "/_protectedLayout/dashboard/": {
      "filePath": "_protectedLayout/dashboard/index.tsx",
      "parent": "/_protectedLayout"
    },
    "/_protectedLayout/users/": {
      "filePath": "_protectedLayout/users/index.tsx",
      "parent": "/_protectedLayout"
    },
    "/_protectedLayout/dashboard/payments/": {
      "filePath": "_protectedLayout/dashboard/payments/index.tsx",
      "parent": "/_protectedLayout"
    },
    "/_protectedLayout/dashboard/pos/": {
      "filePath": "_protectedLayout/dashboard/pos/index.tsx",
      "parent": "/_protectedLayout"
    },
    "/_protectedLayout/dashboard/pos/devices/$deviceId": {
      "filePath": "_protectedLayout/dashboard/pos/devices/$deviceId.tsx",
      "parent": "/_protectedLayout"
    },
    "/_protectedLayout/dashboard/pos/invoicing/new": {
      "filePath": "_protectedLayout/dashboard/pos/invoicing/new.tsx",
      "parent": "/_protectedLayout"
    },
    "/_protectedLayout/dashboard/pos/devices/": {
      "filePath": "_protectedLayout/dashboard/pos/devices/index.tsx",
      "parent": "/_protectedLayout"
    },
    "/_protectedLayout/dashboard/pos/invoicing/": {
      "filePath": "_protectedLayout/dashboard/pos/invoicing/index.tsx",
      "parent": "/_protectedLayout"
    }
  }
}
ROUTE_MANIFEST_END */
