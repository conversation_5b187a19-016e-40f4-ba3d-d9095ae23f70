import { render, screen } from '@testing-library/react';
import { describe, expect, it, vi } from 'vitest';
import { Navigation } from './Navigation';
import { ReactNode } from 'react';

// Mock icons
vi.mock('@/assets/icons', () => ({
  House: () => <div data-testid="icon-house">House</div>,
  HouseFill: () => <div data-testid="icon-house-fill">HouseFill</div>,
  Card: () => <div data-testid="icon-card">Card</div>,
  CardFill: () => <div data-testid="icon-card-fill">CardFill</div>,
  Settings: () => <div data-testid="icon-settings">Settings</div>,
  SettingsFill: () => <div data-testid="icon-settings-fill">SettingsFill</div>,
}));

// Mock navigation components
vi.mock('@/components/ui/navigation-menu.tsx', () => ({
  NavigationMenu: ({ children }: { children: ReactNode }) => (
    <nav data-testid="navigation-menu">{children}</nav>
  ),
  NavigationMenuList: ({ children }: { children: ReactNode }) => (
    <ul data-testid="navigation-menu-list">{children}</ul>
  ),
  NavigationMenuItem: ({ children }: { children: ReactNode }) => (
    <li data-testid="navigation-menu-item">{children}</li>
  ),
  NavigationMenuLink: ({ children, asChild }: { children: ReactNode; asChild?: boolean }) => (
    <div data-testid="navigation-menu-link" data-as-child={asChild}>
      {children}
    </div>
  ),
  navigationMenuTriggerStyle: () => 'navigation-menu-trigger-style',
}));

// Mock router
vi.mock('@tanstack/react-router', () => {
  let currentPathname = '/dashboard';
  
  return {
    useLocation: () => ({
      pathname: currentPathname,
    }),
    Link: ({ to, children, 'data-active': active, title }: { 
      to: string; 
      children: ReactNode; 
      'data-active'?: boolean; 
      title?: string;
      className?: string;
    }) => (
      <a 
        href={to} 
        data-testid={`link-${to.replace(/\//g, '-').substring(1)}`} 
        data-active={active} 
        title={title}
      >
        {children}
      </a>
    ),
    // Helper to set the current pathname for testing
    __setPathname: (pathname: string) => {
      currentPathname = pathname;
    },
  };
});

// Access the mock directly instead of using vi.mocked
// @ts-ignore - We know this exists in our mock
const { __setPathname } = await import('@tanstack/react-router');

describe('Navigation', () => {
  it('renders navigation menu correctly', () => {
    render(<Navigation />);
    
    expect(screen.getByTestId('navigation-menu')).toBeInTheDocument();
    expect(screen.getByTestId('navigation-menu-list')).toBeInTheDocument();
    expect(screen.getAllByTestId('navigation-menu-item').length).toBe(4);
    expect(screen.getAllByTestId('navigation-menu-link').length).toBe(4);
  });

  it('renders all navigation items', () => {
    render(<Navigation />);
    
    expect(screen.getByTestId('link-dashboard')).toBeInTheDocument();
    expect(screen.getByTestId('link-dashboard-payments')).toBeInTheDocument();
    expect(screen.getByTestId('link-dashboard-pos')).toBeInTheDocument();
    expect(screen.getByTestId('link-dashboard-balance')).toBeInTheDocument();
    
    expect(screen.getByTestId('link-dashboard')).toHaveTextContent('Dashboard');
    expect(screen.getByTestId('link-dashboard-payments')).toHaveTextContent('Payments');
    expect(screen.getByTestId('link-dashboard-pos')).toHaveTextContent('POS');
    expect(screen.getByTestId('link-dashboard-balance')).toHaveTextContent('Balance/Account');
  });

  it('shows active state for current route', () => {
    __setPathname('/dashboard');
    
    render(<Navigation />);

    expect(screen.getByTestId('link-dashboard')).toHaveAttribute('data-active', 'true');
    expect(screen.getByTestId('link-dashboard-payments')).not.toHaveAttribute('data-active', 'true');
    expect(screen.getByTestId('link-dashboard-pos')).not.toHaveAttribute('data-active', 'true');
    expect(screen.getByTestId('link-dashboard-balance')).not.toHaveAttribute('data-active', 'true');
  });

  it('renders correct icons for active/inactive', () => {
    __setPathname('/dashboard');
    
    render(<Navigation />);
    
    expect(screen.getByTestId('icon-house-fill')).toBeInTheDocument();
    expect(screen.getByTestId('icon-card')).toBeInTheDocument();
    expect(screen.getAllByTestId('icon-settings').length).toBe(2);
    expect(screen.queryByTestId('icon-house')).not.toBeInTheDocument();
    expect(screen.queryByTestId('icon-card-fill')).not.toBeInTheDocument();
    expect(screen.queryByTestId('icon-settings-fill')).not.toBeInTheDocument();
  });

  it('handles partial route matching correctly', () => {
    __setPathname('/dashboard/payments/123');
    
    render(<Navigation />);
    
    expect(screen.getByTestId('link-dashboard-payments')).toHaveAttribute('data-active', 'true');
    expect(screen.getByTestId('link-dashboard')).not.toHaveAttribute('data-active', 'true');
    expect(screen.getByTestId('link-dashboard-pos')).not.toHaveAttribute('data-active', 'true');
    expect(screen.getByTestId('link-dashboard-balance')).not.toHaveAttribute('data-active', 'true');
  });

  it('handles exact route matching correctly', () => {
    __setPathname('/dashboard/other');
    
    const { unmount } = render(<Navigation />);
    
    expect(screen.getByTestId('link-dashboard')).not.toHaveAttribute('data-active', 'true');
    unmount();
    __setPathname('/dashboard');
    render(<Navigation />);
    expect(screen.getByTestId('link-dashboard')).toHaveAttribute('data-active', 'true');
  });
});