import { render, screen } from '@testing-library/react';
import { describe, expect, it, vi } from 'vitest';
import { Header } from './Header';
import { configureStore } from "@reduxjs/toolkit";
import authReducer from "@/store/slices/authSlice";
import { Provider } from "react-redux";
import { ReactNode } from "react";

// Mock the components used in Header
vi.mock('@/components/ui/logo', () => ({
  Logo: vi.fn(() => <div data-testid="logo-component">Logo</div>)
}));

vi.mock('@/components/ui/profile-dropdown', () => ({
  ProfileDropdown: vi.fn(({ email }) => (
    <div data-testid="profile-dropdown">
      {email && <span data-testid="user-email">{email}</span>}
    </div>
  ))
}));

vi.mock('@/components/layouts/Navigation', () => ({
  Navigation: vi.fn(() => <div data-testid="navigation-component">Navigation</div>)
}));

// Mock router
vi.mock('@tanstack/react-router', () => ({
  Link: vi.fn(({ to, children, className }: { to: string; children: ReactNode; className?: string }) => (
    <a href={to} className={className} data-testid="router-link">
      {children}
    </a>
  )),
  useNavigate: () => vi.fn(),
}));

describe('Header', () => {
  const mockUser = {
    id: '1',
    email: '<EMAIL>',
    name: 'Test User'
  };

  const mockStore = configureStore({
    reducer: {
      auth: authReducer,
    },
    preloadedState: {
      auth: {
        user: mockUser
      }
    }
  });

  const renderWithStore = (ui: React.ReactNode, store = mockStore) => {
    return render(
      <Provider store={store}>
        {ui}
      </Provider>
    );
  };

  it('renders logo and navigation', () => {
    renderWithStore(<Header />);
    
    expect(screen.getByTestId('logo-component')).toBeInTheDocument();
    expect(screen.getByTestId('navigation-component')).toBeInTheDocument();
    expect(screen.getByTestId('router-link')).toHaveAttribute('href', '/');
  });

  it('applies custom className correctly', () => {
    const customClass = 'custom-header-class';
    renderWithStore(<Header className={customClass} />);
    
    const headerElement = screen.getByRole('banner');
    expect(headerElement).toHaveClass(customClass);
  });

  it('displays user email in dropdown', () => {
    renderWithStore(<Header />);
    
    expect(screen.getByTestId('user-email')).toBeInTheDocument();
    expect(screen.getByTestId('user-email')).toHaveTextContent(mockUser.email);
  });
});