import { cn } from '@/lib/utils';
import { ReactNode } from 'react';
import { BackgroundEffect } from '../background-effect/BackgroundEffect';
import { Toaster } from '@/components/ui/toaster';
import { Header } from '@/components/layouts/Header';

interface LayoutProps {
  children: ReactNode;
  className?: string;
}

export function Layout({ children, className }: LayoutProps) {
  return (
    <div className={cn('min-h-screen', className)}>
      <BackgroundEffect />
      <Header />
      <main className="container mx-auto lg:max-w-[1200px] px-4">{children}</main>
      <Toaster />
    </div>
  );
}
