import { render, screen } from '@testing-library/react';
import {describe, expect, it, vi} from 'vitest';
import { Layout } from './Layout';
import {configureStore} from "@reduxjs/toolkit";
import authReducer from "@/store/slices/authSlice";
import {Provider} from "react-redux";

// Mock store
vi.mock('@/store', () => ({
  store: {
    dispatch: vi.fn(),
  },
}));

vi.mock('@tanstack/react-router', () => {
  const React = require('react');
  
  // Create a forwardRef Link component for the mock
  const Link = React.forwardRef(({ to, children, className, ...props }: { 
    to: string; 
    children: React.ReactNode; 
    className?: string; 
    [key: string]: any; 
  }, ref: React.Ref<HTMLAnchorElement>) => {
    return React.createElement(
      'a',
      { 
        href: to, 
        className, 
        'data-testid': 'router-link',
        ref,
        ...props 
      },
      children
    );
  });
  Link.displayName = 'MockedLink';
  
  return {
    Link,
    useNavigate: () => vi.fn(),
    useLocation: () => ({
      pathname: '/dashboard',
      search: {},
      hash: '',
      href: '/dashboard',
      state: {}
    }),
  };
});

describe('Layout', () => {
  const mockStore = configureStore({
    reducer: {
      auth: authReducer,
    },
    preloadedState: {
      auth: {
        user: null
      }
    }
  });

  const renderWithStore = (ui: React.ReactNode) => {
    return render(
      <Provider store={mockStore}>
        {ui}
      </Provider>
    );
  };

  it('renders children correctly', () => {
    const testContent = 'Test Content';
    renderWithStore(
      <Layout>
        <div data-testid="test-child">{testContent}</div>
      </Layout>
    );

    const childElement = screen.getByTestId('test-child');
    expect(childElement).toBeInTheDocument();
    expect(childElement).toHaveTextContent(testContent);
  });

  it('applies custom className correctly', () => {
    renderWithStore(
      <Layout className="test-class">
        <div>Content</div>
      </Layout>
    );

    const layoutContainer = screen.getByRole('main').parentElement;
    expect(layoutContainer).toHaveClass('test-class');
  });
});