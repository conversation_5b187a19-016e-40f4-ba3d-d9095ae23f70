import { render, screen } from '@testing-library/react';
import { useRouter } from '@tanstack/react-router';
import { Page } from './Page';
import '@testing-library/jest-dom';
import {describe, it, expect, vi, beforeEach} from 'vitest';
import React from 'react';

// Mock the getBreadcrumbsForRoute function
vi.mock('@/lib/breadcrumbs', () => ({
  getBreadcrumbsForRoute: vi.fn((pathname, _, data) => {
    if (pathname === '/dashboard/pos/devices/123') {
      if (data === 'Device 123') {
        return [
          { label: 'Dashboard', href: '/dashboard' },
          { label: 'POS', href: '/dashboard/pos' },
          { label: 'POS devices', href: '/dashboard/pos/devices' },
          { label: 'Device 123', href: '/dashboard/pos/devices/123' },
        ];
      } else if (data === 'Custom Device Name') {
        return [
          { label: 'Dashboard', href: '/dashboard' },
          { label: 'POS', href: '/dashboard/pos' },
          { label: 'POS devices', href: '/dashboard/pos/devices' },
          { label: 'Custom Device Name', href: '/dashboard/pos/devices/123' },
        ];
      }
    }
    
    if (pathname === '/dashboard/users') {
      return [
        { label: 'Dashboard', href: '/dashboard' },
        { label: 'Users', href: '/dashboard/users' },
      ];
    }
    
    return [];
  }),
}));

// Mock the router hook and Link component
vi.mock('@tanstack/react-router', () => ({
  useRouter: vi.fn(),
  Link: ({ to, children }: { to: string; children: React.ReactNode }) => (
    <a href={to}>{children}</a>
  ),
}));

describe('Page', () => {
  beforeEach(() => {
    vi.mocked(useRouter).mockReset();
  });

  it('renders the page with correct title', () => {
    vi.mocked(useRouter).mockReturnValue({
      state: {
        location: {
          pathname: '/',
        },
      },
    } as any);

    render(
      <Page title="Test Page">
        <div>Test Content</div>
      </Page>
    );

    expect(screen.getByText('Test Page')).toBeInTheDocument();
    expect(screen.getByText('Test Content')).toBeInTheDocument();
  });

  it('generates breadcrumbs correctly for nested routes', () => {
    vi.mocked(useRouter).mockReturnValue({
      state: {
        location: {
          pathname: '/dashboard/users',
        },
      },
    } as any);

    render(
      <Page title="Users List">
        <div>Users Content</div>
      </Page>
    );

    expect(screen.getByText('Dashboard')).toBeInTheDocument();
    expect(screen.getByText('Users Content')).toBeInTheDocument();
  });

  it('applies custom className when provided', () => {
    vi.mocked(useRouter).mockReturnValue({
      state: {
        location: {
          pathname: '/',
        },
      },
    } as any);

    const { container } = render(
      <Page title="Test Page" className="custom-class">
        <div>Test Content</div>
      </Page>
    );

    expect(container.firstChild).toHaveClass('custom-class');
  });

  it('renders children content', () => {
    vi.mocked(useRouter).mockReturnValue({
      state: {
        location: {
          pathname: '/',
        },
      },
    } as any);

    render(
      <Page title="Test Page">
        <div data-testid="child-content">Custom Child Content</div>
      </Page>
    );

    expect(screen.getByTestId('child-content')).toBeInTheDocument();
    expect(screen.getByText('Custom Child Content')).toBeInTheDocument();
  });

  it('renders actions when provided', () => {
    vi.mocked(useRouter).mockReturnValue({
      state: {
        location: {
          pathname: '/',
        },
      },
    } as any);

    const actionButton = <button data-testid="action-button">Action</button>;

    render(
      <Page title="Test Page" actions={actionButton}>
        <div>Test Content</div>
      </Page>
    );

    expect(screen.getByTestId('action-button')).toBeInTheDocument();
    expect(screen.getByText('Action')).toBeInTheDocument();
  });

  it('handles dynamic breadcrumb labels', () => {
    vi.mocked(useRouter).mockReturnValue({
      state: {
        location: {
          pathname: '/dashboard/pos/devices/123',
        },
        matches: [
          {
            params: { deviceId: '123' },
          },
        ],
      },
    } as any);

    render(
      <Page title="Device Details" breadcrumbData="Device 123">
        <div>Device Content</div>
      </Page>
    );

    expect(screen.getByText('Dashboard')).toBeInTheDocument();
    expect(screen.getByText('POS')).toBeInTheDocument();
    expect(screen.getByText('POS devices')).toBeInTheDocument();
    expect(screen.getByText('Device 123')).toBeInTheDocument();
  });

  it('renders with custom breadcrumbData', () => {
    vi.mocked(useRouter).mockReturnValue({
      state: {
        location: {
          pathname: '/dashboard/pos/devices/123',
        },
        matches: [
          {
            params: { deviceId: '123' },
          },
        ],
      },
    } as any);

    render(
      <Page title="Device Details" breadcrumbData="Custom Device Name">
        <div>Device Content</div>
      </Page>
    );

    expect(screen.getByText('Custom Device Name')).toBeInTheDocument();
  });
}); 