import { VerticalBar } from '@/assets/icons';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbList,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb';
import { cn } from '@/lib/utils';
import { Link, useRouter } from '@tanstack/react-router';
import React, { Fragment } from 'react';
import { getBreadcrumbsForRoute } from '@/lib/breadcrumbs';

interface BreadcrumbItemType {
  label: string;
  href: string;
}

interface PageLayoutProps {
  title: string | React.ReactNode;
  children: React.ReactNode;
  className?: string;
  actions?: React.ReactNode;
  breadcrumbData?: string;
}

export function Page({
  title,
  children,
  className = '',
  actions,
  breadcrumbData,
}: PageLayoutProps) {
  const router = useRouter();

  const generateBreadcrumbs = (): BreadcrumbItemType[] => {
    const pathname = router.state.location.pathname;
    const params = router.state.matches?.[router.state.matches.length - 1]?.params || {};

    return getBreadcrumbsForRoute(pathname, params, breadcrumbData);
  };

  const breadcrumbs = generateBreadcrumbs();

  return (
    <div className={cn('p-6 mx-auto', className)}>
      <div className="flex flex-col rounded-2xl bg-secondary text-card-foreground shadow-sm p-7 w-full min-h-[85vh] gap-10">
        <div className="flex justify-between">
          <div className="flex flex-col gap-2">
            <h1 className="text-3xl text-popover-foreground text-font-space-grotesk leading-none">
              {title}
            </h1>
            <Breadcrumb>
              <BreadcrumbList>
                {breadcrumbs.map((item: BreadcrumbItemType, index: number) => (
                  <Fragment key={item.href}>
                    <BreadcrumbItem className="text-base">
                      <Link to={item.href}>{item.label}</Link>
                    </BreadcrumbItem>
                    {index < breadcrumbs.length - 1 && (
                      <BreadcrumbSeparator>
                        <VerticalBar />
                      </BreadcrumbSeparator>
                    )}
                  </Fragment>
                ))}
              </BreadcrumbList>
            </Breadcrumb>
          </div>
          {actions && <div className="flex items-center gap-3">{actions}</div>}
        </div>
        {children}
      </div>
    </div>
  );
}
