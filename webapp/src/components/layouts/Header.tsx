import { Logo } from '@/components/ui/logo';
import { ProfileDropdown } from '@/components/ui/profile-dropdown';
import { cn } from '@/lib/utils';
import { RootState } from '@/store';
import { Link } from '@tanstack/react-router';
import { useSelector } from 'react-redux';
import { Navigation } from '@/components/layouts/Navigation';

interface HeaderProps {
  className?: string;
}

export function Header({ className }: HeaderProps) {
  const user = useSelector((state: RootState) => state.auth.user);

  return (
    <header className={cn('', className)}>
      <div className="container mx-auto lg:grid lg:grid-cols-[0.5fr_2fr_0.5fr] py-4 justify-between">
        <div className="flex items-center gap-6">
          <Link role="link" to="/">
            <Logo />
          </Link>
        </div>

        <Navigation />

        <div className="flex items-center gap-4">
          <ProfileDropdown email={user?.email} />
        </div>
      </div>
    </header>
  );
}
