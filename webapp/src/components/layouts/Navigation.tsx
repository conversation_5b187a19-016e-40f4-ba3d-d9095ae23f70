'use client';

import { Card, CardFill, House, HouseFill, Settings, SettingsFill } from '@/assets/icons';
import {
  NavigationMenu,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  navigationMenuTriggerStyle,
} from '@/components/ui/navigation-menu.tsx';
import { useLocation, Link } from '@tanstack/react-router';
import { useMemo } from 'react';

export function Navigation() {
  const { pathname } = useLocation();

  const pages = useMemo(
    () => [
      {
        name: 'Dashboard',
        href: '/dashboard',
        title: 'Dashboard',
        Icon: House,
        IconActive: HouseFill,
        partialMatch: false,
      },
      {
        name: 'Payments',
        href: '/dashboard/payments',
        title: 'Payments',
        Icon: Card,
        IconActive: CardFill,
      },
      {
        name: 'POS',
        href: '/dashboard/pos',
        title: 'POS',
        Icon: Settings,
        IconActive: SettingsFill,
      },
      {
        name: 'Balance/Account',
        href: '/dashboard/balance',
        title: 'Balance',
        Icon: Settings,
        IconActive: SettingsFill,
      },
    ],
    []
  );

  const isRouteActive = (href: string, partialMatch = true): boolean => {
    if (partialMatch) {
      return pathname.startsWith(href);
    }
    return pathname === href;
  };

  return (
    <NavigationMenu>
      <NavigationMenuList>
        {pages.map(({ name, href, title, Icon, IconActive, partialMatch = true }) => {
          const isActive = isRouteActive(href, partialMatch);

          return (
            <NavigationMenuItem key={href}>
              <NavigationMenuLink asChild>
                <Link
                  to={href}
                  title={title}
                  data-active={isActive || undefined}
                  className={navigationMenuTriggerStyle()}
                >
                  {isActive ? <IconActive /> : <Icon />}
                  {name}
                </Link>
              </NavigationMenuLink>
            </NavigationMenuItem>
          );
        })}
      </NavigationMenuList>
    </NavigationMenu>
  );
}
