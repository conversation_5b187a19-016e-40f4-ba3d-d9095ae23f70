import { BgRadialLarge, BgRadialSmall } from '@/assets/icons';
import { cn } from '@/lib/utils';

interface BackgroundEffectProps {
  className?: string;
}

export function BackgroundEffect({ className }: BackgroundEffectProps) {
  const radialClasses = 'h-auto w-full max-w-[300px] mx-auto md:max-w-[1200px]';
  return (
    <div
      className={cn(
        'fixed inset-0 flex justify-center overflow-hidden pointer-events-none -z-10',
        className
      )}
      role="figure"
    >
      <div className="w-full max-w-[1200px] text-brand">
        <BgRadialSmall className={cn(radialClasses, 'flex md:hidden text-foreground')} />
        <BgRadialLarge className={cn(radialClasses, 'hidden md:flex text-foreground')} />
      </div>
    </div>
  );
}
