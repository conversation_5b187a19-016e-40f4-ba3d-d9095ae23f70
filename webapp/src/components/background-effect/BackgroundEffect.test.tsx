import { render, screen } from '@testing-library/react';
import { describe, expect, it } from 'vitest';
import { BackgroundEffect } from './BackgroundEffect';

describe('BackgroundEffect', () => {
  it('renders correctly', () => {
    render(<BackgroundEffect />);
    const container = screen.getByRole('figure');
    expect(container).toBeInTheDocument();
    expect(container).toHaveClass('fixed', 'inset-0', 'flex', 'justify-center');
  });

  it('applies custom className correctly', () => {
    render(<BackgroundEffect className="test-class" />);
    const container = screen.getByRole('figure');
    expect(container).toHaveClass('test-class');
  });
}); 