import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Skeleton } from '../ui/skeleton';

// INFO: https://legacy.reactjs.org/docs/lists-and-keys.html#keys
const SKELETON_ROWS = [
  { id: 'skeleton-post-1' },
  { id: 'skeleton-post-2' },
  { id: 'skeleton-post-3' },
  { id: 'skeleton-post-4' },
  { id: 'skeleton-post-5' },
] as const;

export function PostsSkeleton() {
  return (
    <div className="rounded-md border mt-4">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Title</TableHead>
            <TableHead>Body</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {SKELETON_ROWS.map((row) => (
            <TableRow key={row.id}>
              <TableCell>
                <Skeleton className="h-4 w-[250px]" />
              </TableCell>
              <TableCell>
                <Skeleton className="h-4 w-full" />
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}
