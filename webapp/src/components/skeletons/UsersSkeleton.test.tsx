import { render, screen } from '@testing-library/react';
import { describe, expect, it } from 'vitest';
import { UsersSkeleton } from './UsersSkeleton';

describe('UsersSkeleton', () => {
  it('renders the skeleton table with correct headers', () => {
    render(<UsersSkeleton />);

    // Use exact text matching to avoid partial matches
    const headers = screen.getAllByRole('columnheader');
    expect(headers).toHaveLength(3);
    expect(headers[0]).toHaveTextContent('Name');
    expect(headers[1]).toHaveTextContent('Email');
    expect(headers[2]).toHaveTextContent('Username');
  });

  it('renders the correct number of skeleton rows', () => {
    render(<UsersSkeleton />);

    // Each row should have 3 skeleton elements (name, email, and username)
    const skeletonElements = document.querySelectorAll('.h-4');
    expect(skeletonElements).toHaveLength(15); // 5 rows * 3 cells
  });

  it('renders the correct number of skeleton rows', () => {
    render(<UsersSkeleton />);

    const rows = document.querySelectorAll('tbody tr');
    expect(rows).toHaveLength(5);
  });

  it('applies correct width classes to skeleton elements', () => {
    render(<UsersSkeleton />);

    const nameSkeletons = document.querySelectorAll('.w-\\[140px\\]');
    const emailSkeletons = document.querySelectorAll('.w-\\[180px\\]');
    const usernameSkeletons = document.querySelectorAll('.w-\\[120px\\]');

    expect(nameSkeletons).toHaveLength(5);
    expect(emailSkeletons).toHaveLength(5);
    expect(usernameSkeletons).toHaveLength(5);
  });
});
