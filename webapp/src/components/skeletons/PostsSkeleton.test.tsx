import { render, screen } from '@testing-library/react';
import { describe, expect, it } from 'vitest';
import { PostsSkeleton } from './PostsSkeleton';

describe('PostsSkeleton', () => {
  it('renders the skeleton table with correct headers', () => {
    render(<PostsSkeleton />);

    // Use exact text matching to avoid partial matches
    const headers = screen.getAllByRole('columnheader');
    expect(headers).toHaveLength(2);
    expect(headers[0]).toHaveTextContent('Title');
    expect(headers[1]).toHaveTextContent('Body');
  });

  it('renders the correct number of skeleton rows', () => {
    render(<PostsSkeleton />);

    // Each row should have 2 skeleton elements (title and body)
    const skeletonElements = document.querySelectorAll('.h-4');
    expect(skeletonElements).toHaveLength(10); // 5 rows * 2 cells
  });

  it('renders the correct number of skeleton rows', () => {
    render(<PostsSkeleton />);

    const rows = document.querySelectorAll('tbody tr');
    expect(rows).toHaveLength(5);
  });

  it('applies correct width classes to skeleton elements', () => {
    render(<PostsSkeleton />);

    // Use more specific selectors to target only the skeleton elements in table cells
    const titleSkeletons = document.querySelectorAll('tbody td:first-child .w-\\[250px\\]');
    const bodySkeletons = document.querySelectorAll('tbody td:last-child .w-full');

    expect(titleSkeletons).toHaveLength(5);
    expect(bodySkeletons).toHaveLength(5);
  });
});
