import { Check, Copy } from 'lucide-react';
import { Button, type ButtonProps } from './button';
import { useState } from 'react';

interface Props extends ButtonProps {
  value: string | number;
  text?: string;
}

function CopyButton({ value, children, ...props }: Readonly<Props>) {
  const [copied, setCopied] = useState(false);

  function handleClick() {
    navigator.clipboard.writeText(String(value));
    setCopied(true);
    setTimeout(() => {
      setCopied(false);
    }, 2500);
  }

  return (
    <Button variant="outline" size="sm" onClick={handleClick} className="w-fit" {...props}>
      {children}
      {copied ? <Check /> : <Copy />}
    </Button>
  );
}

export { CopyButton };
