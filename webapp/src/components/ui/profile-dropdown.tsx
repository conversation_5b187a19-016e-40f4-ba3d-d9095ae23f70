import { ChevronDown } from '@/assets/icons';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { logout } from '@/store/slices/authSlice';
import { Link, useNavigate } from '@tanstack/react-router';
import { UserIcon } from 'lucide-react';
import { useLayoutEffect, useRef, useState } from 'react';
import { useDispatch } from 'react-redux';

interface UserMenuProps {
  email?: string;
}

export function ProfileDropdown({ email }: UserMenuProps) {
  const triggerRef = useRef<HTMLButtonElement>(null);
  const [buttonWidth, setButtonWidth] = useState<number>(0);
  const dispatch = useDispatch();
  const navigate = useNavigate();

  useLayoutEffect(() => {
    if (triggerRef.current) {
      setButtonWidth(triggerRef.current.offsetWidth);
    }
  }, []);

  const handleLogout = () => {
    dispatch(logout());
    navigate({ to: '/login' });
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          ref={triggerRef}
          role="button"
          variant="ghost"
          className="flex gap-2 border-0 w-full"
        >
          <UserIcon />
          <span className="sr-only">Profile</span>
          {email && <p className="text-sm">{email}</p>}
          <ChevronDown title="Log out" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent style={{ width: buttonWidth ? `${buttonWidth}px` : 'auto' }}>
        <DropdownMenuItem>
          <Link
            role="link"
            to="/"
            className="w-full cursor-pointer hover:bg-accent hover:text-accent-foreground"
          >
            Profile
          </Link>
        </DropdownMenuItem>
        <DropdownMenuItem
          className="cursor-pointer hover:bg-accent hover:text-accent-foreground"
          onClick={handleLogout}
        >
          Log out
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
