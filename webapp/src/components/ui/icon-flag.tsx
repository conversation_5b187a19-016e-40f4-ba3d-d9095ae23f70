import flags from 'react-phone-number-input/flags';
import labels from 'react-phone-number-input/locale/en.json';

interface Props {
  country: keyof typeof flags;
}

/**
 * A functional component that renders a flag icon based on the provided country code.
 *
 * @param {Readonly<Props>} props - The properties object containing the country code.
 * @param {keyof typeof flags} props.country - The country code used to select the appropriate flag.
 * @returns {JSX.Element | null} The flag icon as a JSX element, or null if the flag is not found.
 */
export default function IconFlag({ country }: Readonly<Props>) {
  const Flag = flags[country];
  if (!Flag) return null;
  return <Flag title={labels[country]} />;
}
