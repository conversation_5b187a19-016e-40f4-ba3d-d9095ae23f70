import IconFlag from '@/components/ui/icon-flag';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import { Button } from '@/components/ui/button';
import { ChevronDown } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useEffect, useState, useRef } from 'react';
import type flags from 'react-phone-number-input/flags';
import { getCountries } from 'react-phone-number-input/input';
import labels from 'react-phone-number-input/locale/en.json';

type Country = {
  code: string;
  name: string;
};

type CountrySelectProps = {
  onChange: (value: string) => void;
  value?: string;
};

export function CountrySelect({ onChange, value }: CountrySelectProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedCountry, setSelectedCountry] = useState<Country | null>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const allCountries = getCountries()
    .map((country) => ({
      code: country,
      name: labels[country],
    }))
    .sort((a, b) => a.name.localeCompare(b.name));

  useEffect(() => {
    if (value && !selectedCountry) {
      const country = allCountries.find((c) => c.code === value);
      if (country) {
        setSelectedCountry(country);
      }
    }
  }, [value, selectedCountry, allCountries]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleCountrySelect = (country: Country) => {
    setSelectedCountry(country);
    onChange(country.code);
    setIsOpen(false);
  };

  const toggleDropdown = () => {
    setIsOpen(!isOpen);
  };

  return (
    <div className="relative w-full" ref={dropdownRef}>
      <Button
        type="button"
        variant="outline"
        onClick={toggleDropdown}
        className="w-full justify-between h-12 py-5 px-3 bg-muted/50"
      >
        {selectedCountry ? (
          <div className="flex items-center gap-2">
            <IconFlag country={selectedCountry.code as keyof typeof flags} />
            <span>{selectedCountry.name}</span>
          </div>
        ) : (
          <span className="text-muted-foreground">Choose your country</span>
        )}
        <ChevronDown className={cn('h-4 w-4 transition-transform', isOpen && 'rotate-180')} />
      </Button>

      {isOpen && (
        <div className="absolute left-0 right-0 z-50 mt-1 shadow-lg overflow-hidden">
          <Command>
            <CommandInput placeholder="Search countries..." className="border-none" />
            <CommandList>
              <CommandEmpty>No country found.</CommandEmpty>
              <CommandGroup>
                {allCountries.map((country) => (
                  <CommandItem
                    key={country.code}
                    value={country.name}
                    onSelect={() => handleCountrySelect(country)}
                    className="cursor-pointer"
                  >
                    <IconFlag country={country.code as keyof typeof flags} />
                    <span className="ml-2">{country.name}</span>
                  </CommandItem>
                ))}
              </CommandGroup>
            </CommandList>
          </Command>
        </div>
      )}
    </div>
  );
}
