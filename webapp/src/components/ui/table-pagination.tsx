import { Ellipsis } from '@/assets/icons';
import { Button } from '@/components/ui/button';
import { Table as ReactTable } from '@tanstack/react-table';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { useMemo } from 'react';

interface DataTablePaginationProps<TData> {
  table: ReactTable<TData>;
}

export function DataTablePagination<TData>({ table }: DataTablePaginationProps<TData>) {
  const currentPage = table.getState().pagination.pageIndex;
  const totalPages = table.getPageCount();

  const pageButtons = useMemo(() => {
    const buttons: React.ReactNode[] = [];
    let startEllipsisInserted = false;
    let endEllipsisInserted = false;

    const renderEllipsis = (key: string) => (
      <Button
        key={key}
        variant="square"
        size="xs"
        disabled
        className="align-text-bottom cursor-default"
        aria-label="More pages"
      >
        <Ellipsis />
      </Button>
    );

    for (let i = 0; i < totalPages; i++) {
      const isFirstOrLast = i === 0 || i === totalPages - 1;
      const isAdjacentToCurrent = Math.abs(i - currentPage) <= 1;

      if (isFirstOrLast || isAdjacentToCurrent) {
        buttons.push(
          <Button
            key={`page-${i}`}
            variant={currentPage === i ? 'default' : 'square'}
            size="xs"
            onClick={() => table.setPageIndex(i)}
            aria-current={currentPage === i ? 'page' : undefined}
            aria-label={`Go to page ${i + 1}`}
          >
            {i + 1}
          </Button>
        );
      } else if (i < currentPage && !startEllipsisInserted) {
        buttons.push(renderEllipsis('ellipsis-start'));
        startEllipsisInserted = true;
      } else if (i > currentPage && !endEllipsisInserted) {
        buttons.push(renderEllipsis('ellipsis-end'));
        endEllipsisInserted = true;
      }
    }

    return buttons;
  }, [currentPage, totalPages, table]);

  return (
    <div className="flex items-center justify-between">
      <div className="flex gap-1">
        <Button
          variant="square"
          size="xs"
          onClick={() => table.previousPage()}
          disabled={!table.getCanPreviousPage()}
          aria-label="Previous page"
        >
          <ChevronLeft strokeWidth="3" />
        </Button>

        {pageButtons}

        <Button
          variant="square"
          size="xs"
          onClick={() => table.nextPage()}
          disabled={!table.getCanNextPage()}
          aria-label="Next page"
        >
          <ChevronRight strokeWidth="3" />
        </Button>
      </div>

      <div className="flex w-fit items-center justify-center text-sm font-medium uppercase">
        Page {currentPage + 1} of {totalPages}
      </div>
    </div>
  );
}
