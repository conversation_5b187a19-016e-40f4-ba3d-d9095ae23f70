import { LogoITO, LogoSymbol, LogoWeChain, LogoWefi } from '@/assets/icons';
import { cn } from '@/lib/utils';

const variants = {
  ito: LogoITO,
  symbol: LogoSymbol,
  wechain: LogoWeChain,
  wefi: LogoWefi,
};

export type LogoProps = {
  variant?: keyof typeof variants;
  className?: string;
};

export const Logo = ({ variant = 'wefi', className, ...props }: LogoProps) => {
  const LogoComponent = variants[variant];
  return <LogoComponent className={cn('bg-icon-brand', className)} {...props} />;
};
