import { UseQueryResult } from '@tanstack/react-query';
import { AlertCircle } from 'lucide-react';
import { Alert, AlertDescription } from '../ui/alert';

interface QueryWrapperProps<TData, TError> {
  query: UseQueryResult<TData, TError>;
  children: (data: TData) => React.ReactNode;
  skeleton?: React.ReactNode;
}

export function QueryWrapper<TData, TError>({
  query,
  children,
  skeleton,
}: QueryWrapperProps<TData, TError>) {
  if (query.isLoading) {
    return skeleton || <div>Loading...</div>;
  }

  if (query.isError) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          {query.error instanceof Error
            ? query.error.message
            : 'An error occurred while fetching the data'}
        </AlertDescription>
      </Alert>
    );
  }

  if (!query.data) {
    return null;
  }

  return <>{children(query.data)}</>;
}
