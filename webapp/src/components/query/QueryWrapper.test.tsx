import { UseQueryResult } from '@tanstack/react-query';
import { render, screen } from '@testing-library/react';
import { describe, expect, it } from 'vitest';
import { QueryWrapper } from './QueryWrapper';

describe('QueryWrapper', () => {
  const mockData = { id: 1, name: 'Test' };
  const mockChildren = (data: typeof mockData) => <div>Data: {data.name}</div>;
  const mockSkeleton = <div>Custom Loading...</div>;

  // Create a minimal mock that matches what QueryWrapper actually uses
  function createMockQuery(
    options: Partial<
      Pick<UseQueryResult<typeof mockData, Error>, 'data' | 'isLoading' | 'isError' | 'error'>
    >
  ): UseQueryResult<typeof mockData, Error> {
    return {
      data: undefined,
      isLoading: false,
      isError: false,
      error: null,
      ...options,
    } as UseQueryResult<typeof mockData, Error>;
  }

  it('renders loading state with default loading message', () => {
    const query = createMockQuery({ isLoading: true });
    render(<QueryWrapper query={query}>{mockChildren}</QueryWrapper>);

    expect(screen.getByText('Loading...')).toBeInTheDocument();
  });

  it('renders custom skeleton in loading state', () => {
    const query = createMockQuery({ isLoading: true });
    render(
      <QueryWrapper query={query} skeleton={mockSkeleton}>
        {mockChildren}
      </QueryWrapper>
    );

    expect(screen.getByText('Custom Loading...')).toBeInTheDocument();
  });

  it('renders error state with error message', () => {
    const errorMessage = 'Failed to fetch data';
    const query = createMockQuery({
      isError: true,
      error: new Error(errorMessage),
    });

    render(<QueryWrapper query={query}>{mockChildren}</QueryWrapper>);

    expect(screen.getByText(errorMessage)).toBeInTheDocument();
  });

  it('renders error state with default message for non-Error objects', () => {
    const query = createMockQuery({
      isError: true,
      error: 'string error' as any,
    });

    render(<QueryWrapper query={query}>{mockChildren}</QueryWrapper>);

    expect(screen.getByText('An error occurred while fetching the data')).toBeInTheDocument();
  });

  it('renders nothing when data is undefined', () => {
    const query = createMockQuery({ data: undefined });
    const { container } = render(<QueryWrapper query={query}>{mockChildren}</QueryWrapper>);

    expect(container.firstChild).toBeNull();
  });

  it('renders children with data when query is successful', () => {
    const query = createMockQuery({ data: mockData });
    render(<QueryWrapper query={query}>{mockChildren}</QueryWrapper>);

    expect(screen.getByText(`Data: ${mockData.name}`)).toBeInTheDocument();
  });

  it('prioritizes loading state over error state', () => {
    const query = createMockQuery({
      isLoading: true,
      isError: true,
      error: new Error('Should not show'),
    });

    render(<QueryWrapper query={query}>{mockChildren}</QueryWrapper>);

    expect(screen.getByText('Loading...')).toBeInTheDocument();
    expect(screen.queryByText('Should not show')).not.toBeInTheDocument();
  });

  it('renders error alert with icon', () => {
    const query = createMockQuery({
      isError: true,
      error: new Error('Test error'),
    });

    render(<QueryWrapper query={query}>{mockChildren}</QueryWrapper>);

    // Check for the alert and icon
    expect(screen.getByRole('alert')).toBeInTheDocument();
    expect(document.querySelector('.h-4.w-4')).toBeInTheDocument();
  });
});
