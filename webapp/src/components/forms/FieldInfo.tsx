import { AnyFieldApi } from '@tanstack/react-form';

export default function FieldInfo({ field }: { field: AnyFieldApi }) {
  const errors = field.state.meta.errors.map((error) => error.message);

  return (
    <>
      {field.state.meta.isTouched && field.state.meta.isBlurred && !field.state.meta.isValid ? (
        <span className="text-destructive text-sm mt-1">{errors.join(',')}</span>
      ) : null}
      {field.state.meta.isValidating ? 'Validating...' : null}
    </>
  );
}
