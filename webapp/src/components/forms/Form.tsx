import { createFormHook } from '@tanstack/react-form';
import { lazy } from 'react';
import { fieldContext, formContext, useFormContext } from './FormContext.tsx';

const TextField = lazy(() => import('./fields/TextField.tsx'));
const CountrySelectField = lazy(() => import('./fields/CountrySelectField.tsx'));

function SubscribeButton({ label }: { label: string }) {
  const form = useFormContext();
  return (
    <form.Subscribe selector={(state) => state.isSubmitting}>
      {(isSubmitting) => (
        <button type="submit" disabled={isSubmitting}>
          {label}
        </button>
      )}
    </form.Subscribe>
  );
}

export const { useAppForm, withForm } = createFormHook({
  fieldComponents: {
    TextField,
    CountrySelectField,
  },
  formComponents: {
    SubscribeButton,
  },
  fieldContext,
  formContext,
});
