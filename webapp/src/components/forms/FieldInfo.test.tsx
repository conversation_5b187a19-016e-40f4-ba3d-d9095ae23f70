import { render, screen } from '@testing-library/react';
import { describe, expect, it } from 'vitest';
import FieldInfo from './FieldInfo';

describe('FieldInfo', () => {
  const createMockField = (overrides = {}) => ({
    state: {
      meta: {
        isTouched: false,
        isBlurred: false,
        isValidating: false,
        isValid: true,
        errors: [],
        ...overrides,
      },
    },
  });

  it('shows nothing when field is untouched', () => {
    const field = createMockField();
    const { container } = render(<FieldInfo field={field as any} />);
    expect(container.firstChild).toBeNull();
  });

  it('shows validation message when field is touched, blurred and invalid', () => {
    const field = createMockField({
      isTouched: true,
      isBlurred: true,
      isValid: false,
      errors: [{ message: 'Required field' }],
    });

    render(<FieldInfo field={field as any} />);
    expect(screen.getByText('Required field')).toBeInTheDocument();
  });

  it('shows multiple error messages joined by comma', () => {
    const field = createMockField({
      isTouched: true,
      isBlurred: true,
      isValid: false,
      errors: [{ message: 'Required field' }, { message: 'Must be at least 3 characters' }],
    });

    render(<FieldInfo field={field as any} />);
    expect(screen.getByText('Required field,Must be at least 3 characters')).toBeInTheDocument();
  });

  it('shows validating message when field is validating', () => {
    const field = createMockField({
      isValidating: true,
    });

    render(<FieldInfo field={field as any} />);
    expect(screen.getByText('Validating...')).toBeInTheDocument();
  });

  it('shows nothing when field is valid', () => {
    const field = createMockField({
      isTouched: true,
      isBlurred: true,
      isValid: true,
    });

    const { container } = render(<FieldInfo field={field as any} />);
    expect(container.firstChild).toBeNull();
  });
});
