import { render, screen, fireEvent } from '@testing-library/react';
import { describe, expect, it, vi, beforeEach } from 'vitest';
import TextField from './TextField';
import * as FormContext from '../FormContext';

describe('TextField', () => {
  const mockField = {
    name: 'test-field',
    state: {
      value: '',
      meta: {
        isTouched: false,
        isBlurred: false,
        isValidating: false,
        isValid: true,
        errors: [],
        isDirty: false,
        errorMap: {},
        errorSourceMap: {},
      },
    },
    handleBlur: vi.fn(),
    handleChange: vi.fn(),
  };

  beforeEach(() => {
    vi.spyOn(FormContext, 'useFieldContext').mockReturnValue(mockField as any);
  });

  it('renders with label and input', () => {
    render(<TextField label="Test Field" />);

    expect(screen.getByLabelText('Test Field')).toBeInTheDocument();
    expect(screen.getByRole('textbox')).toBeInTheDocument();
  });

  it('renders with custom className', () => {
    const { container } = render(<TextField label="Test Field" className="custom-class" />);

    // Find the wrapper div directly since it's the first div in the container
    const wrapperDiv = container.querySelector('div');
    expect(wrapperDiv).toHaveClass('custom-class');
  });

  it('handles different input types', () => {
    render(<TextField label="Password" type="password" />);

    expect(screen.getByLabelText('Password')).toHaveAttribute('type', 'password');
  });

  it('calls handleChange on input change', () => {
    render(<TextField label="Test Field" />);

    const input = screen.getByRole('textbox');
    fireEvent.change(input, { target: { value: 'new value' } });

    expect(mockField.handleChange).toHaveBeenCalledWith('new value');
  });

  it('calls handleBlur on input blur', () => {
    render(<TextField label="Test Field" />);

    const input = screen.getByRole('textbox');
    fireEvent.blur(input);

    expect(mockField.handleBlur).toHaveBeenCalled();
  });

  it('displays validation state correctly', () => {
    const fieldWithError = {
      ...mockField,
      state: {
        value: '',
        meta: {
          isTouched: true,
          isBlurred: true,
          isValidating: false,
          isValid: false,
          errors: [{ message: 'Required field' }],
          isDirty: true,
          errorMap: {},
          errorSourceMap: {},
        },
      },
    };

    vi.spyOn(FormContext, 'useFieldContext').mockReturnValue(fieldWithError as any);

    render(<TextField label="Test Field" />);

    expect(screen.getByText('Required field')).toBeInTheDocument();
  });

  it('shows validating state', () => {
    const validatingField = {
      ...mockField,
      state: {
        value: '',
        meta: {
          isTouched: false,
          isBlurred: false,
          isValidating: true,
          isValid: true,
          errors: [],
          isDirty: false,
          errorMap: {},
          errorSourceMap: {},
        },
      },
    };

    vi.spyOn(FormContext, 'useFieldContext').mockReturnValue(validatingField as any);

    render(<TextField label="Test Field" />);

    expect(screen.getByText('Validating...')).toBeInTheDocument();
  });
});
