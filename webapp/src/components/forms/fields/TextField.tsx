import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { cn } from '@/lib/utils';
import FieldInfo from '../FieldInfo';
import { useFieldContext } from '../FormContext';

export default function TextField({
  label,
  className,
  type = 'text',
  placeholder,
}: {
  label: string;
  className?: string;
  type?: 'text' | 'email' | 'password';
  placeholder?: string;
}) {
  const field = useFieldContext<string>();

  return (
    <div className={cn(className)}>
      <Label htmlFor={field.name}>{label}</Label>
      <Input
        id={field.name}
        name={field.name}
        type={type}
        placeholder={placeholder}
        value={field.state.value}
        onBlur={field.handleBlur}
        onChange={(e) => field.handleChange(e.target.value)}
      />

      <FieldInfo field={field} />
    </div>
  );
}
