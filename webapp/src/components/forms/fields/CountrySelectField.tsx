import { Label } from '@/components/ui/label';
import { CountrySelect } from '@/components/ui/country-select';
import { cn } from '@/lib/utils';
import FieldInfo from '../FieldInfo';
import { useFieldContext } from '../FormContext';

export default function CountrySelectField({
  label,
  className,
}: {
  label: string;
  className?: string;
}) {
  const field = useFieldContext<string>();

  return (
    <div className={cn(className)}>
      <Label htmlFor={field.name}>{label}</Label>
      <CountrySelect value={field.state.value} onChange={(value) => field.handleChange(value)} />
      <FieldInfo field={field} />
    </div>
  );
}
