import { render, screen, fireEvent } from '@testing-library/react';
import { describe, expect, it, vi, beforeEach } from 'vitest';
import CountrySelectField from './CountrySelectField';
import * as FormContext from '../FormContext';
import { CountrySelect } from '@/components/ui/country-select';

// Mock the CountrySelect component
vi.mock('@/components/ui/country-select', () => ({
  CountrySelect: vi.fn(() => <div data-testid="country-select">Country Select Mock</div>),
}));

describe('CountrySelectField', () => {
  const mockField = {
    name: 'country',
    state: {
      value: '',
      meta: {
        isTouched: false,
        isBlurred: false,
        isValidating: false,
        isValid: true,
        errors: [],
        isDirty: false,
        errorMap: {},
        errorSourceMap: {},
      },
    },
    handleBlur: vi.fn(),
    handleChange: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
    vi.spyOn(FormContext, 'useFieldContext').mockReturnValue(mockField as any);
    (CountrySelect as any).mockImplementation(({ value, onChange }: { value: string; onChange: (value: string) => void }) => (
      <div data-testid="country-select" onClick={() => onChange('US')}>
        Country Select Mock (value: {value})
      </div>
    ));
  });

  it('renders with label', () => {
    render(<CountrySelectField label="Country" />);

    expect(screen.getByText('Country')).toBeInTheDocument();
    expect(screen.getByTestId('country-select')).toBeInTheDocument();
  });

  it('applies custom className', () => {
    const { container } = render(<CountrySelectField label="Country" className="custom-class" />);

    // Find the wrapper div directly since it's the first div in the container
    const wrapperDiv = container.querySelector('div');
    expect(wrapperDiv).toHaveClass('custom-class');
  });

  it('displays CountrySelect with value', () => {
    const fieldWithValue = {
      ...mockField,
      state: {
        ...mockField.state,
        value: 'US',
      },
    };

    vi.spyOn(FormContext, 'useFieldContext').mockReturnValue(fieldWithValue as any);

    render(<CountrySelectField label="Country" />);

    expect(CountrySelect).toHaveBeenCalledWith(
      expect.objectContaining({
        value: 'US',
      }),
      expect.anything()
    );
  });

  it('handles country selection change', () => {
    render(<CountrySelectField label="Country" />);

    fireEvent.click(screen.getByTestId('country-select'));
    expect(mockField.handleChange).toHaveBeenCalledWith('US');
  });
});