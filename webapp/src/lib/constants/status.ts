export const PAYMENT_STATUS = {
  text: {
    PAID: 'Completed',
    PENDING: 'Pending',
    EXPIRED: 'Expired',
    CANCELED: 'Canceled',
    UNDERPAID: 'Underpaid',
    REFUNDED: 'Refunded',
    FAILED: 'Failed',
    BLOCKED: 'Blocked',
  },
  colors: {
    PAID: 'bg-success',
    PENDING: 'bg-warning',
    EXPIRED: 'bg-error',
    CANCELED: 'bg-warning',
    UNDERPAID: 'bg-error',
    REFUNDED: 'bg-warning',
    FAILED: 'bg-error',
    BLOCKED: 'bg-error',
  },
};

export const POS_DEVICE_STATUS = {
  text: {
    PAIRED: 'Paired',
    UNPAIRED: 'Not Paired',
  },
  colors: {
    PAIRED: 'bg-success',
    UNPAIRED: 'bg-muted border-error text-error',
  },
};

export function getPaymentStatus(status: string | undefined) {
  return PAYMENT_STATUS.text[status as keyof typeof PAYMENT_STATUS.text] ?? '';
}

export function getPaymentStatusColor(status: string | undefined) {
  return PAYMENT_STATUS.colors[status as keyof typeof PAYMENT_STATUS.colors] ?? '';
}

export function getPosDeviceStatus(status: string | undefined) {
  return POS_DEVICE_STATUS.text[status as keyof typeof POS_DEVICE_STATUS.text] ?? '';
}

export function getPosDeviceStatusColor(status: string | undefined) {
  return POS_DEVICE_STATUS.colors[status as keyof typeof POS_DEVICE_STATUS.colors] ?? '';
}
