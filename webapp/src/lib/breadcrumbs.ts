interface BreadcrumbConfig {
  label: string;
  href: string;
  dynamic?: boolean;
}

interface BreadcrumbRoute {
  pattern: string;
  breadcrumbs: BreadcrumbConfig[];
  getDynamicLabel?: (params: Record<string, string>, data?: string) => string;
}

export const breadcrumbRoutes: BreadcrumbRoute[] = [
  {
    pattern: '/dashboard',
    breadcrumbs: [{ label: 'Dashboard', href: '/dashboard' }],
  },
  {
    pattern: '/dashboard/pos',
    breadcrumbs: [
      { label: 'Dashboard', href: '/dashboard' },
      { label: 'POS', href: '/dashboard/pos' },
    ],
  },
  {
    pattern: '/dashboard/pos/devices',
    breadcrumbs: [
      { label: 'Dashboard', href: '/dashboard' },
      { label: 'POS', href: '/dashboard/pos' },
      { label: 'POS devices', href: '/dashboard/pos/devices' },
    ],
  },
  {
    pattern: '/dashboard/pos/devices/:deviceId',
    breadcrumbs: [
      { label: 'Dashboard', href: '/dashboard' },
      { label: 'POS', href: '/dashboard/pos' },
      { label: 'POS devices', href: '/dashboard/pos/devices' },
      { label: '', href: '', dynamic: true },
    ],
    getDynamicLabel: (_, name) => name || 'Device',
  },
];

/**
 * Generates a list of breadcrumbs for a given route.
 *
 * This function matches the provided pathname against predefined breadcrumb routes.
 * If a match is found, it returns the corresponding breadcrumbs. If no match is found,
 * it auto-generates breadcrumbs based on the pathname segments.
 *
 * @param pathname - The current route pathname.
 * @param params - Optional parameters extracted from the route.
 * @param data - Optional additional data used for dynamic breadcrumb labels.
 * @returns An array of breadcrumb configurations.
 */
export function getBreadcrumbsForRoute(
  pathname: string,
  params?: Record<string, string>,
  data?: string
) {
  // Find matching route pattern
  const route = breadcrumbRoutes.find((route) => {
    const pattern = route.pattern.replace(/:([^/]+)/g, '[^/]+');
    const regex = new RegExp(`^${pattern}$`);
    return regex.test(pathname);
  });

  if (!route) {
    // Fallback to auto-generated breadcrumbs
    const paths = pathname.split('/').filter(Boolean);
    return paths.map((path, index) => ({
      label: path.charAt(0).toUpperCase() + path.slice(1),
      href: `/${paths.slice(0, index + 1).join('/')}`,
    }));
  }

  return route.breadcrumbs.map((breadcrumb) => {
    if (breadcrumb.dynamic && route.getDynamicLabel) {
      return {
        label: route.getDynamicLabel(params || {}, data),
        href: pathname,
      };
    }
    return breadcrumb;
  });
}
