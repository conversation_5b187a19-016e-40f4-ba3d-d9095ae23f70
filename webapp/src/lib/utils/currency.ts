// TODO: change to use the user's locale
export const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
  }).format(amount);
};

export const formatFiatAmount = (amount: number | undefined, currency: string | undefined) => {
  return amount && currency
    ? new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: currency,
      }).format(amount)
    : undefined;
};
