/**
 * Returns `value` if it’s not null/undefined/empty; otherwise returns fallback.
 * Works for strings, numbers, booleans, and any object (checks only for null/undefined/empty string).
 */
export function safeRender<T>(
  value: T | null | undefined,
  fallback = '—'
): T extends string ? string : T | string {
  if (value === null || value === undefined) {
    return fallback as T extends string ? string : T | string;
  }

  if (typeof value === 'string') {
    return (value.trim().length > 0 ? value : fallback) as T extends string ? string : T | string;
  }

  return value as T extends string ? string : T | string;
}

/**
 * Checks if any of the fields on an object are missing, null, undefined, or empty string.
 */
export function hasIncompleteFields<T extends object>(obj: T): boolean {
  return Object.values(obj).some((value) => {
    return value === null || value === undefined || value === '';
  });
}
