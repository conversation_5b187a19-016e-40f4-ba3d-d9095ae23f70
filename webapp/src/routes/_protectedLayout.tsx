import { Layout } from '@/components/layouts/Layout';
import { store } from '@/store';
import { selectIsAuthenticated } from '@/store/slices/authSlice';
import { Outlet, createFileRoute, redirect } from '@tanstack/react-router';
const isAuthenticated = () => {
  return selectIsAuthenticated(store.getState());
};

export const Route = createFileRoute('/_protectedLayout')({
  component: RouteComponent,
  beforeLoad: async ({ location }) => {
    if (!isAuthenticated()) {
      throw redirect({
        to: '/login',
        search: {
          // Use the current location to power a redirect after login
          // (Do not use `router.state.resolvedLocation` as it can
          // potentially lag behind the actual current location)
          redirect: location.href,
        },
      });
    }
  },
});

function RouteComponent() {
  return (
    <div className="flex flex-col h-screen max-h-screen">
      <Layout>
        <div className="container  mx-auto flex-1 overflow-y-auto">
          <Outlet />
        </div>
      </Layout>
    </div>
  );
}
