import { AuthContainer } from '@/features/auth/components/AuthContainer';
import { useAppDispatch } from '@/store/hooks';
import { setCurrentStep } from '@/store/slices/authSlice';
import { createFileRoute, useNavigate } from '@tanstack/react-router';
import { useEffect } from 'react';

export const Route = createFileRoute('/signup')({
  component: RouteComponent,
});

function RouteComponent() {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();

  // Set initial step to sign-up when accessing this route
  useEffect(() => {
    dispatch(setCurrentStep('sign-up'));
  }, [dispatch]);

  const handleAuthSuccess = () => {
    // Redirect to dashboard after successful authentication
    navigate({ to: '/dashboard' });
  };

  return <AuthContainer onSuccess={handleAuthSuccess} />;
}
