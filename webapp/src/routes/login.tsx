import { AuthContainer } from '@/features/auth/components/AuthContainer';
import { createFileRoute, useNavigate } from '@tanstack/react-router';
import { useAppDispatch } from '@/store/hooks';
import { setCurrentStep } from '@/store/slices/authSlice';
import { useEffect } from 'react';

export const Route = createFileRoute('/login')({
  component: RouteComponent,
});

function RouteComponent() {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();

  // Set initial step to sign-in when accessing this route
  useEffect(() => {
    dispatch(setCurrentStep('sign-in'));
  }, [dispatch]);

  const handleAuthSuccess = () => {
    // Redirect to dashboard after successful authentication
    navigate({ to: '/dashboard' });
  };

  return <AuthContainer onSuccess={handleAuthSuccess} />;
}
