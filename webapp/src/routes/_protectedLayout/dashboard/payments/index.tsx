import { createFileRoute } from '@tanstack/react-router';
import { PaymentsTable } from '@/features/dashboard/components/payments/PaymentsTable';
import { usePaymentsQuery } from '@/features/dashboard/hooks/usePaymentsQuery';
import { PaymentColumns } from '@/features/dashboard/components/payments/TableColumns';

export const Route = createFileRoute('/_protectedLayout/dashboard/payments/')({
  component: RouteComponent,
});

function RouteComponent() {
  const { data: payments, isLoading, isError } = usePaymentsQuery();

  return (
    <PaymentsTable
      columns={PaymentColumns}
      data={payments ?? []}
      title="Payments"
      isLoading={isLoading}
      isError={isError}
    />
  );
}
