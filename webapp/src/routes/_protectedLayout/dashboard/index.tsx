import { ActionBar } from '@/features/dashboard/components/ActionBar';
import { Balance } from '@/features/dashboard/components/Balance';
import { PosCard } from '@/features/dashboard/components/pos/PosCard.tsx';
import { LastPaymentsCard } from '@/features/dashboard/components/payments/LastPaymentsCard';
import { PaymentsCard } from '@/features/dashboard/components/payments/PaymentsCard';
import { useDashboard } from '@/hooks/use-dashboard';
import { createFileRoute } from '@tanstack/react-router';
import { LoadingState } from '@/features/dashboard/components/LoadingState';

export const Route = createFileRoute('/_protectedLayout/dashboard/')({
  component: RouteComponent,
});

function RouteComponent() {
  const { data, loading, error } = useDashboard();

  if (loading) {
    return <LoadingState />;
  }

  if (error || !data) {
    return (
      <div className="container py-8">
        <div className="flex items-center justify-center h-32">
          <p className="text-2xl text-card-foreground">No data available</p>
        </div>
      </div>
    );
  }

  return (
    <div className="py-2 space-y-6">
      <ActionBar />
      <div className="grid gap-5 lg:grid-cols-2 md:grid-cols-1">
        <PaymentsCard data={data.paymentsData} />
        <LastPaymentsCard data={data.paymentsData} />
        <Balance data={data.balanceData} />
        <PosCard data={data.posData} />
      </div>
    </div>
  );
}
