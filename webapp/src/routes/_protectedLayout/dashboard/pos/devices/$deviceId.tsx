import { createFileRoute, useNavigate } from '@tanstack/react-router';
import { Page } from '@/components/layouts/Page';
import { LoadingState } from '@/features/dashboard/components/LoadingState';
import { EmptyState } from '@/features/dashboard/components/EmptyState';
import { usePosDeviceManagementQuery } from '@/features/dashboard/hooks/usePosDeviceManagementQuery';
import { Button } from '@/components/ui/button';
import { ChevronLeft } from 'lucide-react';
import { PosDeviceForm } from '@/features/dashboard/components/pos/PosDeviceForm';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { getPosDeviceStatus, getPosDeviceStatusColor } from '@/lib/constants/status';
import { safeRender } from '@/lib/utils/stringUtils';
import { PosDeviceStatus } from '@/features/dashboard/types';

export const Route = createFileRoute('/_protectedLayout/dashboard/pos/devices/$deviceId')({
  component: RouteComponent,
});

function RouteComponent() {
  const { deviceId } = Route.useParams();
  const navigate = useNavigate();
  const { device, isLoading, isError } = usePosDeviceManagementQuery(deviceId);

  if (isLoading) {
    return (
      <Page title="Loading Device...">
        <LoadingState />
      </Page>
    );
  }

  if (isError) {
    return (
      <Page title="Error Loading Device">
        <EmptyState type="error" context="pos" />
      </Page>
    );
  }

  const handleBack = () => {
    navigate({ to: '/dashboard/pos/devices' });
  };

  if (!device) {
    return (
      <Page title="Device Not Found">
        <div className="flex flex-col items-center justify-center h-64">
          <p className="text-muted-foreground mb-4">Device not found</p>
          <Button onClick={handleBack} variant="outline">
            <ChevronLeft className="h-4 w-4 mr-2" />
            Back to Devices
          </Button>
        </div>
      </Page>
    );
  }

  const title = (name: string, status: PosDeviceStatus) => (
    <div className="flex items-center gap-1">
      <Button variant="link" size="default" onClick={handleBack} className="h-8 w-8">
        <ChevronLeft strokeWidth={4} size={20} />
      </Button>
      <div className="flex flex-row items-center gap-3">
        {name}
        <Badge
          className={cn(
            getPosDeviceStatusColor(status),
            'font-space-grotesk rounded-2xs text-xs px-1'
          )}
        >
          {safeRender(getPosDeviceStatus(status))}
        </Badge>
      </div>
    </div>
  );

  const actionButton = (status: PosDeviceStatus) => (
    <Button variant={status === 'PAIRED' ? 'ghost' : 'default'}>
      {status === 'PAIRED' ? 'Pair again' : 'Pair'}
    </Button>
  );

  return (
    <Page
      title={title(device.businessName, device.status)}
      actions={actionButton(device.status)}
      breadcrumbData={device.businessName}
    >
      <PosDeviceForm device={device} />
    </Page>
  );
}
