import { createFileRoute, useNavigate } from '@tanstack/react-router';
import { usePosDevicesQuery } from '@/features/dashboard/hooks/usePosDevicesQuery';
import { POSDevice } from '@/features/dashboard/types';
import { PosDevicesTable } from '@/features/dashboard/components/pos/PosDevicesTable';
import { PosDevicesColumns } from '@/features/dashboard/components/pos/TableColumns';

export const Route = createFileRoute('/_protectedLayout/dashboard/pos/devices/')({
  component: RouteComponent,
});

function RouteComponent() {
  const { data: devices, isLoading, isError } = usePosDevicesQuery();
  const navigate = useNavigate();

  const handleRowClick = (device: POSDevice) => {
    navigate({
      to: '/dashboard/pos/devices/$deviceId',
      params: { deviceId: device.id },
    });
  };

  return (
    <PosDevicesTable
      columns={PosDevicesColumns}
      data={devices ?? []}
      title="POS devices"
      isLoading={isLoading}
      isError={isError}
      onRowClick={handleRowClick}
    />
  );
}
