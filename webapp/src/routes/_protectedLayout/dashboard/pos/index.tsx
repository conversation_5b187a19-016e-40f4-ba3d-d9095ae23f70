import { createFileRoute } from '@tanstack/react-router';
import { PosDashboard } from '@/features/dashboard/components/pos/PosDashboard.tsx';
import { usePosQuery } from '@/features/dashboard/hooks/usePosQuery.ts';

export const Route = createFileRoute('/_protectedLayout/dashboard/pos/')({
  component: POSLayout,
});

function POSLayout() {
  const { data: pos, isLoading, isError } = usePosQuery();
  return <PosDashboard data={pos} title="POS" isLoading={isLoading} isError={isError} />;
}
