import { Button } from '@/components/ui/button';
import { createFileRoute } from '@tanstack/react-router';
import { Outlet } from '@tanstack/react-router';
import { Link } from '@tanstack/react-router';

export const Route = createFileRoute('/_protectedLayout/dashboard/pos/invoicing/')({
  component: RootComponent,
});

function RootComponent() {
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-semibold">Invoicing</h2>
        <Button asChild>
          <Link to="/dashboard/pos/invoicing/new">New Invoice</Link>
        </Button>
      </div>
      <Outlet />
    </div>
  );
}
