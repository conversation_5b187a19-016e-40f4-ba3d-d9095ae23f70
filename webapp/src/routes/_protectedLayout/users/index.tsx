import { QueryWrapper } from '@/components/query/QueryWrapper';
import { UsersSkeleton } from '@/components/skeletons/UsersSkeleton';
import { UsersTable } from '@/features/users/components/UsersTable';
import { useUsersQuery } from '@/features/users/hooks/useUsersQuery';
import { createFileRoute } from '@tanstack/react-router';

export const Route = createFileRoute('/_protectedLayout/users/')({
  component: RouteComponent,
});

function RouteComponent() {
  const usersQuery = useUsersQuery();

  return (
    <div className="p-4 max-w-6xl mx-auto">
      <h1 className="text-2xl font-bold">Users</h1>
      <QueryWrapper query={usersQuery} skeleton={<UsersSkeleton />}>
        {(users) => <UsersTable users={users} />}
      </QueryWrapper>
    </div>
  );
}
