import { QueryWrapper } from '@/components/query/QueryWrapper';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Post } from '@/lib/types/post';
import { Todo } from '@/lib/types/todo';
import { useQuery } from '@tanstack/react-query';
import { createFileRoute } from '@tanstack/react-router';
import { ArrowLeftIcon, CheckCircle2, Circle } from 'lucide-react';

export const Route = createFileRoute('/_protectedLayout/users/$userId')({
  component: RouteComponent,
});

interface UserData {
  id: number;
  name: string;
  email: string;
  username: string;
}

interface UserDetailResponse {
  userData: UserData;
  userPosts: Post[];
  userTodos: Todo[];
}

function RouteComponent() {
  const { userId } = Route.useParams();
  const navigate = Route.useNavigate();

  const userQuery = useQuery<UserDetailResponse>({
    queryKey: ['user', userId],
    queryFn: async () => {
      const userData = await fetch(`https://jsonplaceholder.typicode.com/users/${userId}`).then(
        (res) => res.json()
      );
      const userPosts = await fetch(
        `https://jsonplaceholder.typicode.com/posts?userId=${userId}`
      ).then((res) => res.json());
      const userTodos = await fetch(
        `https://jsonplaceholder.typicode.com/todos?userId=${userId}`
      ).then((res) => res.json());
      return { userData, userPosts, userTodos };
    },
  });

  return (
    <div className="p-4 max-w-6xl mx-auto">
      <QueryWrapper query={userQuery} skeleton={<div>Loading...</div>}>
        {({ userData, userPosts, userTodos }) => {
          return (
            <div>
              <div className="flex justify-between">
                <div>
                  <h1 className="text-2xl font-bold">{userData.name}</h1>
                  <p className="text-sm text-gray-500">{userData.email}</p>
                </div>
                <Button variant="outline" onClick={() => navigate({ to: '/users' })}>
                  <ArrowLeftIcon className="w-4 h-4" />
                  Back
                </Button>
              </div>

              <div className="grid grid-cols-4 gap-4 mt-10">
                <div className="col-span-3">
                  <Card>
                    <CardHeader>
                      <h1 className="text-lg font-bold">Posts</h1>
                    </CardHeader>
                    <CardContent>
                      <ul>
                        {userPosts.map((post, index) => (
                          <div key={post.id}>
                            <h3 className="text-lg font-bold">{post.title}</h3>
                            <p className="text-sm text-gray-500">{post.body}</p>
                            {index !== userPosts.length - 1 && <Separator className="mb-4 mt-2" />}
                          </div>
                        ))}
                      </ul>
                    </CardContent>
                  </Card>
                </div>
                <div className="col-span-1">
                  <Card>
                    <CardHeader>
                      <h1 className="text-lg font-bold">Todos</h1>
                    </CardHeader>
                    <CardContent>
                      <ul className="space-y-2">
                        {userTodos.map((todo) => (
                          <li
                            key={todo.id}
                            className={`flex items-start gap-2 ${
                              todo.completed ? 'text-gray-500' : 'text-gray-900'
                            }`}
                          >
                            {todo.completed ? (
                              <CheckCircle2 className="h-5 w-5 text-green-500 flex-shrink-0 mt-0.5" />
                            ) : (
                              <Circle className="h-5 w-5 text-gray-400 flex-shrink-0 mt-0.5" />
                            )}
                            <span className={todo.completed ? 'line-through' : ''}>
                              {todo.title}
                            </span>
                          </li>
                        ))}
                      </ul>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </div>
          );
        }}
      </QueryWrapper>
    </div>
  );
}
