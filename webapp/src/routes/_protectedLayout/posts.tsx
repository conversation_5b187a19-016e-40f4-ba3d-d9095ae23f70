import { QueryWrapper } from '@/components/query/QueryWrapper';
import { PostsSkeleton } from '@/components/skeletons/PostsSkeleton';
import { PostsTable } from '@/features/posts/components/PostsTable';
import { usePostsQuery } from '@/features/posts/hooks/usePostsQuery';
import { createFileRoute } from '@tanstack/react-router';

export const Route = createFileRoute('/_protectedLayout/posts')({
  component: RouteComponent,
});

function RouteComponent() {
  const postsQuery = usePostsQuery();

  return (
    <div className="p-4 max-w-6xl mx-auto">
      <h1 className="text-2xl font-bold">Posts</h1>
      <QueryWrapper query={postsQuery} skeleton={<PostsSkeleton />}>
        {(posts) => <PostsTable posts={posts} />}
      </QueryWrapper>
    </div>
  );
}
