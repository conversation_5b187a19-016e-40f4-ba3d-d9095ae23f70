import { AuthContainer } from '@/features/auth/components/AuthContainer';
import { createFileRoute, useNavigate } from '@tanstack/react-router';

export const Route = createFileRoute('/auth')({
  component: RouteComponent,
});

function RouteComponent() {
  const navigate = useNavigate();

  const handleAuthSuccess = () => {
    // Redirect to dashboard after successful authentication
    navigate({ to: '/dashboard' });
  };

  return <AuthContainer onSuccess={handleAuthSuccess} />;
}
