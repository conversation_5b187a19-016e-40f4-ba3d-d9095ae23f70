import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { renderHook, act } from '@testing-library/react';
import { useToast } from './use-toast';

describe('useToast hook', () => {
  beforeEach(() => {
    vi.useFakeTimers();
  });

  afterEach(() => {
    vi.restoreAllMocks();
    vi.clearAllTimers();
    vi.useRealTimers();
  });

  it('should add toast with basic properties', () => {
    const { result } = renderHook(() => useToast());
    
    act(() => {
      result.current.toast({
        title: 'Test Toast',
        description: 'This is a test description',
        variant: 'default',
      });
    });
    
    expect(result.current.toasts).toHaveLength(1);
    expect(result.current.toasts[0].title).toBe('Test Toast');
    expect(result.current.toasts[0].description).toBe('This is a test description');
    expect(result.current.toasts[0].variant).toBe('default');
    expect(result.current.toasts[0].id).toBeDefined();
    expect(result.current.toasts[0].open).toBe(true);
  });

  it('should update existing toast', () => {
    const { result } = renderHook(() => useToast());
    let toastId: string;
    let updateToast: (props: any) => void;
    
    act(() => {
      const response = result.current.toast({
        title: 'Initial Title',
        description: 'Initial description',
      });
      toastId = response.id;
      updateToast = response.update;
    });
    
    expect(result.current.toasts[0].title).toBe('Initial Title');
    
    act(() => {
      updateToast({
        id: toastId,
        title: 'Updated Title',
        description: 'Updated description',
      });
    });
    
    expect(result.current.toasts).toHaveLength(1);
    expect(result.current.toasts[0].title).toBe('Updated Title');
    expect(result.current.toasts[0].description).toBe('Updated description');
  });

  it('should dismiss toast by ID', () => {
    const { result } = renderHook(() => useToast());
    let toastId: string;
    
    act(() => {
      const response = result.current.toast({
        title: 'Test Toast',
      });
      toastId = response.id;
    });
    
    expect(result.current.toasts).toHaveLength(1);
    expect(result.current.toasts[0].open).toBe(true);
    
    act(() => {
      result.current.dismiss(toastId);
    });
    
    expect(result.current.toasts[0].open).toBe(false);
    
    // After the removal delay, the toast should be removed
    act(() => {
      vi.advanceTimersByTime(10000);
    });
    
    expect(result.current.toasts).toHaveLength(0);
  });

  it('should dismiss all toasts', () => {
    const { result } = renderHook(() => useToast());
    
    // Add multiple toasts
    act(() => {
      result.current.toast({ title: 'Toast 1' });
      result.current.toast({ title: 'Toast 2' });
    });
    
    // Due to TOAST_LIMIT = 1, we should only have one toast
    expect(result.current.toasts).toHaveLength(1);
    expect(result.current.toasts[0].title).toBe('Toast 2');
    
    act(() => {
      result.current.dismiss(); // Dismiss all
    });
    
    expect(result.current.toasts[0].open).toBe(false);
    
    // After the removal delay, all toasts should be removed
    act(() => {
      vi.advanceTimersByTime(10000);
    });
    
    expect(result.current.toasts).toHaveLength(0);
  });
});