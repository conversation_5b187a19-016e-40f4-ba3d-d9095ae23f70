import type { AppDispatch, RootState } from '@/store';
import { fetchDashboardData } from '@/store/dashboard/dashboardSlice';
import { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';

export function useDashboard() {
  const dispatch = useDispatch<AppDispatch>();
  const { data, loading, error } = useSelector((state: RootState) => state.dashboard);

  useEffect(() => {
    dispatch(fetchDashboardData());
  }, [dispatch]);

  return { data, loading, error };
}
