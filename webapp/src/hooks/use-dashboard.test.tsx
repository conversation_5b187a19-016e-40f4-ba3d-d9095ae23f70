import { describe, it, expect, vi, beforeEach } from 'vitest';
import { renderHook } from '@testing-library/react';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import { dashboardReducer } from '@/store/dashboard/dashboardSlice';
import { useDashboard } from './use-dashboard';
import * as DashboardSlice from '@/store/dashboard/dashboardSlice';

describe('useDashboard (integration test)', () => {
    beforeEach(() => {
        vi.restoreAllMocks(); // reset all mocks before each test
    });

    const createTestStore = (preloadedState = {}) =>
        configureStore({
            reducer: {
                dashboard: dashboardReducer,
            },
            preloadedState,
        });

    it('should dispatch fetchDashboardData on mount', () => {
        const fetchSpy = vi.spyOn(DashboardSlice, 'fetchDashboardData');

        const store = createTestStore();

        renderHook(() => useDashboard(), {
            wrapper: ({ children }) => <Provider store={store}>{children}</Provider>,
        });

        expect(fetchSpy).toHaveBeenCalledTimes(1);
    });
});
