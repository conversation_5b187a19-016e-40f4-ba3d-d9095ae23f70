import React from 'react';
import ReactDOM from 'react-dom/client';
import { describe, expect, it, vi } from 'vitest';

// Mock ReactDOM
vi.mock('react-dom/client', () => {
  const createRoot = vi.fn(() => ({
    render: vi.fn(),
  }));
  return {
    createRoot,
    default: { createRoot },
  };
});

describe('main', () => {
  it('renders the app in strict mode', async () => {
    const root = document.createElement('div');
    root.id = 'root';
    document.body.appendChild(root);

    await import('./main');

    expect(ReactDOM.createRoot).toHaveBeenCalledWith(root);

    const mockCreateRoot = ReactDOM.createRoot as unknown as ReturnType<typeof vi.fn>;
    const { render } = mockCreateRoot.mock.results[0].value;
    expect(render).toHaveBeenCalledWith(
      expect.objectContaining({
        type: React.StrictMode,
        props: expect.objectContaining({
          children: expect.any(Object),
        }),
      })
    );

    document.body.removeChild(root);
  });
});
