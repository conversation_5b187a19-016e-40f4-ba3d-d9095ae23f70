import React from 'react';
import ReactDOM from 'react-dom/client';
import { Provider } from 'react-redux';
import App from './App';
import { store } from './store';
import { initializeTokenManagement } from './features/auth/utils/tokenManager';
import { setTokens } from './store/slices/authSlice';

import './styles/globals.css';

const rootElement = document.getElementById('root');
if (!rootElement) throw new Error('Failed to find the root element');

document.documentElement.classList.add('dark');

// Initialize token management and restore auth state
const tokens = initializeTokenManagement();
console.log(tokens, 'tokens !!!')
if (tokens) {
  // Restore tokens to Redux state
  store.dispatch(setTokens(tokens));

  // If we have valid tokens, user is likely authenticated
  // In a real app, you would validate the token with the server
  // For now, we'll redirect to dashboard if tokens exist
  if (window.location.pathname === '/login' || window.location.pathname === '/signup') {
    window.location.href = '/dashboard';
  }
}

const root = ReactDOM.createRoot(rootElement);
root.render(
  <React.StrictMode>
    <Provider store={store}>
      <App />
    </Provider>
  </React.StrictMode>
);
