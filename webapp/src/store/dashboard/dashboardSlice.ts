import type { DashboardData } from '@/lib/types/dashboard';
import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';

interface DashboardState {
  data: DashboardData | null;
  loading: boolean;
  error: string | null;
}

const initialState: DashboardState = {
  data: null,
  loading: false,
  error: null,
};

// Async thunk for fetching dashboard data
export const fetchDashboardData = createAsyncThunk('dashboard/fetchData', async () => {
  // TODO: Replace with actual API call
  const mockData: DashboardData = {
    paymentsData: [
      {
        id: '1',
        address: '0x0b123456789ABC123456789ABC123456789ABCd',
        amount: 1500.5,
        date: '2025-05-26',
        payment_method: 'POS',
        coin: {
          coin_id: 'usdt-bep20',
          name_short: 'USDT',
        },
        application: {
          name: 'Online Store',
        },
        processed_at: Date.now(),
        amount_fiat: 1500.5,
        currency_fiat: 'USD',
        status: 'PAID',
      },
      {
        id: '2',
        address: '0x0b123456789ABC123456789ABC123456789ABCd',
        amount: 500.0,
        date: '2025-05-25',
        payment_method: 'QR',
        coin: {
          coin_id: 'usdt-bep20',
          name_short: 'USDT',
        },
        application: {
          name: 'Service Payment',
        },
        processed_at: Date.now() - 86400000,
        amount_fiat: 500.0,
        currency_fiat: 'USD',
        status: 'PENDING',
      },
      {
        id: '3',
        address: '0x0b123456789ABC123456789ABC123456789ABCd',
        amount: 2550,
        date: '2025-05-24',
        payment_method: 'Invoice',
        coin: {
          coin_id: 'usdt-trc20',
          name_short: 'USDT',
        },
        application: {
          name: 'POS Terminal #42',
        },
        processed_at: Date.now() - 86400000 * 2,
        amount_fiat: 2550,
        currency_fiat: 'USD',
        status: 'EXPIRED',
      },
      {
        id: '4',
        address: '0x0b123456789ABC123456789ABC123456789ABCd',
        amount: 250,
        date: '2025-05-21',
        payment_method: 'API',
        coin: {
          coin_id: 'usdt-trc20',
          name_short: 'USDT',
        },
        application: {
          name: 'POS Terminal #43',
        },
        processed_at: Date.now() - 86400000 * 5,
        amount_fiat: 250,
        currency_fiat: 'USD',
        status: 'UNDERPAID',
      },
    ],
    balanceData: [
      {
        id: '1',
        name: 'Ethereum network',
        currency: 'USDT',
        coin_id: 'usdt-bep20',
        amount: 1500.5,
      },
      { id: '2', name: 'Bitcoin network', currency: 'BTC', coin_id: 'usdt-bep20', amount: 0.042 },
      { id: '3', name: 'Ethereum', currency: 'ETH', coin_id: 'usdt-trc20', amount: 0 },
    ],
    posData: [
      { id: '1', name: 'Terminals', amount: 10, type: 'DEVICE' },
      { id: '2', name: 'API', amount: 0, type: 'API' },
      { id: '3', name: 'Ecommerce', amount: 4, type: 'ECOMMERCE' },
      { id: '4', name: 'Invoices', amount: 314, type: 'INVOICE' },
      { id: '5', name: 'QR codes', amount: 41, type: 'QR' },
    ],
  };

  // Simulate API delay
  await new Promise((resolve) => setTimeout(resolve, 1000));
  return mockData;
});

const dashboardSlice = createSlice({
  name: 'dashboard',
  initialState,
  reducers: {
    // Add any synchronous reducers here if needed
    clearDashboard: (state) => {
      state.data = null;
      state.loading = false;
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchDashboardData.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchDashboardData.fulfilled, (state, action) => {
        state.loading = false;
        state.data = action.payload;
      })
      .addCase(fetchDashboardData.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to fetch dashboard data';
      });
  },
});

export const { clearDashboard } = dashboardSlice.actions;
export const dashboardReducer = dashboardSlice.reducer;
