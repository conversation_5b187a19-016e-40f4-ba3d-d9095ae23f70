import { beforeEach, describe, expect, it } from 'vitest';
import { clearDashboard, fetchDashboardData } from './dashboard/dashboardSlice';
import { store } from './index';
import { logout, selectIsAuthenticated, setUser, setTokens } from './slices/authSlice';

describe('Redux Store', () => {
  it('should create store with initial state', () => {
    const state = store.getState();
    expect(state).toEqual({
      auth: {
        user: null,
        tokens: null,
        isAuthenticated: false,
        isLoading: false,
        error: null,
        currentStep: 'sign-in',
        signUpEmail: null,
      },
      dashboard: {
        data: null,
        error: null,
        loading: false,
      },
    });
  });

  it('should have correct types and methods', () => {
    expect(store.dispatch).toBeDefined();
    expect(store.getState).toBeDefined();
    expect(store.subscribe).toBeDefined();
  });

  describe('Auth Slice', () => {
    it('should handle user login', () => {
      const testUser = {
        id: '123',
        email: '<EMAIL>',
      };

      store.dispatch(setUser(testUser));

      const state = store.getState();
      expect(state.auth.user).toEqual(testUser);
      expect(selectIsAuthenticated(state)).toBe(true);
    });

    it('should handle user logout', () => {
      // First set a user and tokens
      store.dispatch(
        setUser({
          id: '123',
          email: '<EMAIL>',
        })
      );
      store.dispatch(setTokens({ accessToken: 'token', refreshToken: 'refresh' }));

      // Then logout
      store.dispatch(logout());

      const state = store.getState();
      expect(state.auth.user).toBeNull();
      expect(state.auth.tokens).toBeNull();
      expect(selectIsAuthenticated(state)).toBe(false);
    });

    it('should handle setting user to null', () => {
      store.dispatch(setUser(null));

      const state = store.getState();
      expect(state.auth.user).toBeNull();
      expect(selectIsAuthenticated(state)).toBe(false);
    });
  });

  describe('Dashboard Slice', () => {
    beforeEach(() => {
      // Clear the store before each test
      store.dispatch(clearDashboard());
    });

    it('should have correct initial state', () => {
      const state = store.getState();
      expect(state.dashboard).toEqual({
        data: null,
        loading: false,
        error: null,
      });
    });

    it('should handle fetchDashboardData.pending', () => {
      store.dispatch(fetchDashboardData.pending(''));
      const state = store.getState();

      expect(state.dashboard.loading).toBe(true);
      expect(state.dashboard.error).toBeNull();
    });

    it('should handle fetchDashboardData.fulfilled', async () => {
      const mockData = {
        paymentsData: [],
        balanceData: [],
        posData: [],
      };

      await store.dispatch(fetchDashboardData.fulfilled(mockData, ''));
      const state = store.getState();

      expect(state.dashboard.loading).toBe(false);
      expect(state.dashboard.data).toEqual(mockData);
      expect(state.dashboard.error).toBeNull();
    });

    it('should handle fetchDashboardData.rejected', () => {
      const errorMessage = 'Failed to fetch data';
      store.dispatch(fetchDashboardData.rejected(new Error(errorMessage), ''));
      const state = store.getState();

      expect(state.dashboard.loading).toBe(false);
      expect(state.dashboard.data).toBeNull();
      expect(state.dashboard.error).toBe(errorMessage);
    });

    it('should handle clearDashboard', () => {
      // First set some data
      store.dispatch(
        fetchDashboardData.fulfilled(
          {
            paymentsData: [],
            balanceData: [],
            posData: [],
          },
          ''
        )
      );

      // Then clear it
      store.dispatch(clearDashboard());
      const state = store.getState();

      expect(state.dashboard).toEqual({
        data: null,
        loading: false,
        error: null,
      });
    });

    it('should fetch dashboard data successfully', async () => {
      const result = await store.dispatch(fetchDashboardData());
      const state = store.getState();

      expect(result.type).toBe(fetchDashboardData.fulfilled.type);
      expect(state.dashboard.data).toBeDefined();
      expect(state.dashboard.loading).toBe(false);
      expect(state.dashboard.error).toBeNull();

      // Verify the structure of the fetched data
      expect(state.dashboard.data).toHaveProperty('paymentsData');
      expect(state.dashboard.data).toHaveProperty('balanceData');
      expect(state.dashboard.data).toHaveProperty('posData');
    });
  });
});
