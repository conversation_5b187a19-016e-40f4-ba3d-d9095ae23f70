import { PayloadAction, createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { RootState } from '..';
import {
  AuthState,
  User,
  JWTTokens,
  AuthStep,
  SignUpFormData,
  SignInFormData,
  OTPFormData,
} from '@/features/auth/types';
import {
  signUpApi,
  signInApi,
  verifyOTPApi,
  resendOTPApi,
  refreshTokenApi,
  logoutApi,
} from '@/features/auth/api/mockAuthApi';

const initialState: AuthState = {
  user: null,
  tokens: null,
  isAuthenticated: false,
  isLoading: false,
  error: null,
  currentStep: 'sign-in',
  signUpEmail: null,
};

// Async thunks for auth operations
export const signUp = createAsyncThunk(
  'auth/signUp',
  async (data: SignUpFormData, { rejectWithValue }) => {
    const response = await signUpApi(data);
    if (!response.success) {
      return rejectWithValue(response.error || 'Sign up failed');
    }
    return { ...response.data, email: data.email };
  }
);

export const signIn = createAsyncThunk(
  'auth/signIn',
  async (data: SignInFormData, { rejectWithValue }) => {
    const response = await signInApi(data);
    if (!response.success) {
      return rejectWithValue(response.error || 'Sign in failed');
    }
    return response.data;
  }
);

export const verifyOTP = createAsyncThunk(
  'auth/verifyOTP',
  async ({ email, code }: { email: string; code: OTPFormData }, { rejectWithValue }) => {
    const response = await verifyOTPApi(email, code);
    if (!response.success) {
      return rejectWithValue(response.error || 'OTP verification failed');
    }
    return response.data;
  }
);

export const resendOTP = createAsyncThunk(
  'auth/resendOTP',
  async (email: string, { rejectWithValue }) => {
    const response = await resendOTPApi(email);
    if (!response.success) {
      return rejectWithValue(response.error || 'Failed to resend OTP');
    }
    return response.data;
  }
);

// Wallet creation thunk (mock implementation)
export const createWallet = createAsyncThunk(
  'auth/createWallet',
  async ({ coin, chain }: { coin: string; chain: string }, { getState, rejectWithValue }) => {
    // Simulate wallet creation
    console.log(`Creating wallet for coin: ${coin}, chain: ${chain}`);
    await new Promise(resolve => setTimeout(resolve, 2000));

    const state = getState() as RootState;
    const signUpEmail = state.auth.signUpEmail;

    if (!signUpEmail) {
      return rejectWithValue('No email found for wallet creation');
    }

    // Import the API function to complete registration
    const { submitBusinessDetailsApi } = await import('@/features/auth/api/mockAuthApi');

    // Complete the registration with minimal business details
    const response = await submitBusinessDetailsApi(signUpEmail, {
      firstName: 'User',
      lastName: 'Name',
      addressLine1: '123 Main St',
      state: 'CA',
      city: 'San Francisco',
      zipCode: '94102',
    });

    if (!response.success) {
      return rejectWithValue(response.error || 'Failed to complete registration');
    }

    // Generate tokens for the user
    const { generateMockTokens } = await import('@/features/auth/api/mockAuthApi');

    if (!response.data?.user) {
      return rejectWithValue('User data not found');
    }

    const tokens = generateMockTokens(response.data.user.id, response.data.user.email);

    return {
      user: response.data.user,
      tokens,
      message: 'Wallet created successfully'
    };
  }
);

export const refreshToken = createAsyncThunk(
  'auth/refreshToken',
  async (_, { getState, rejectWithValue }) => {
    const state = getState() as RootState;
    const refreshTokenValue = state.auth.tokens?.refreshToken;

    if (!refreshTokenValue) {
      return rejectWithValue('No refresh token available');
    }

    const response = await refreshTokenApi(refreshTokenValue);
    if (!response.success) {
      return rejectWithValue(response.error || 'Token refresh failed');
    }
    return response.data;
  }
);

export const logout = createAsyncThunk(
  'auth/logout',
  async (_, { rejectWithValue }) => {
    const response = await logoutApi();
    if (!response.success) {
      return rejectWithValue(response.error || 'Logout failed');
    }
    return response.data;
  }
);

export const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    // Legacy action for backward compatibility
    setUser: (state, action: PayloadAction<User | null>) => {
      state.user = action.payload;
      state.isAuthenticated = action.payload !== null;
    },
    setTokens: (state, action: PayloadAction<JWTTokens | null>) => {
      state.tokens = action.payload;
    },
    setCurrentStep: (state, action: PayloadAction<AuthStep>) => {
      state.currentStep = action.payload;
    },
    setSignUpEmail: (state, action: PayloadAction<string | null>) => {
      state.signUpEmail = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    },
    resetAuthState: (state) => {
      state.user = null;
      state.tokens = null;
      state.isAuthenticated = false;
      state.isLoading = false;
      state.error = null;
      state.currentStep = 'sign-in';
      state.signUpEmail = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Sign Up
      .addCase(signUp.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(signUp.fulfilled, (state, action) => {
        state.isLoading = false;
        state.signUpEmail = action.payload.email;
        state.currentStep = 'otp-verification';
      })
      .addCase(signUp.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })

      // Sign In
      .addCase(signIn.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(signIn.fulfilled, (state, action) => {
        state.isLoading = false;
        if (action.payload) {
          state.user = action.payload.user;
          state.tokens = action.payload.tokens;
          state.isAuthenticated = true;
          state.currentStep = 'completed';
          // Save tokens to localStorage
          if (typeof window !== 'undefined') {
            import('@/features/auth/utils/tokenManager').then(({ tokenStorage }) => {
              if (action.payload?.tokens) {
                tokenStorage.setTokens(action.payload.tokens);
              }
            });
          }
        }
      })
      .addCase(signIn.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })

      // OTP Verification
      .addCase(verifyOTP.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(verifyOTP.fulfilled, (state) => {
        state.isLoading = false;
        state.currentStep = 'email-confirmed';
      })
      .addCase(verifyOTP.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })

      // Resend OTP
      .addCase(resendOTP.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(resendOTP.fulfilled, (state) => {
        state.isLoading = false;
      })
      .addCase(resendOTP.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })

      // Wallet Creation
      .addCase(createWallet.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(createWallet.fulfilled, (state, action) => {
        state.isLoading = false;
        // Set user and tokens if they exist in the payload
        if (action.payload.user && action.payload.tokens) {
          state.user = action.payload.user;
          state.tokens = action.payload.tokens;
          state.isAuthenticated = true;
          state.currentStep = 'completed';
          // Save tokens to localStorage
          if (typeof window !== 'undefined') {
            import('@/features/auth/utils/tokenManager').then(({ tokenStorage }) => {
              tokenStorage.setTokens(action.payload.tokens);
            });
          }
        } else {
          state.currentStep = 'wallet-created';
        }
      })
      .addCase(createWallet.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })

      // Refresh Token
      .addCase(refreshToken.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(refreshToken.fulfilled, (state, action) => {
        state.isLoading = false;
        if (action.payload) {
          state.tokens = action.payload;
          // Save new tokens to localStorage
          if (typeof window !== 'undefined') {
            import('@/features/auth/utils/tokenManager').then(({ tokenStorage }) => {
              if (action.payload) {
                tokenStorage.setTokens(action.payload);
              }
            });
          }
        }
      })
      .addCase(refreshToken.rejected, (state) => {
        state.isLoading = false;
        state.user = null;
        state.tokens = null;
        state.isAuthenticated = false;
        state.currentStep = 'sign-in';
        // Clear tokens from localStorage
        if (typeof window !== 'undefined') {
          import('@/features/auth/utils/tokenManager').then(({ tokenStorage }) => {
            tokenStorage.clearTokens();
          });
        }
      })

      // Logout
      .addCase(logout.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(logout.fulfilled, (state) => {
        state.isLoading = false;
        state.user = null;
        state.tokens = null;
        state.isAuthenticated = false;
        state.currentStep = 'sign-in';
        state.signUpEmail = null;
        state.error = null;
        // Clear tokens from localStorage
        if (typeof window !== 'undefined') {
          import('@/features/auth/utils/tokenManager').then(({ tokenStorage }) => {
            tokenStorage.clearTokens();
          });
        }
      })
      .addCase(logout.rejected, (state) => {
        state.isLoading = false;
        // Even if logout fails, clear local state
        state.user = null;
        state.tokens = null;
        state.isAuthenticated = false;
        state.currentStep = 'sign-in';
        state.signUpEmail = null;
        // Clear tokens from localStorage
        if (typeof window !== 'undefined') {
          import('@/features/auth/utils/tokenManager').then(({ tokenStorage }) => {
            tokenStorage.clearTokens();
          });
        }
      });
  },
});

// Selectors
export const selectIsAuthenticated = (state: RootState): boolean => state.auth.isAuthenticated;
export const selectUser = (state: RootState): User | null => state.auth.user;
export const selectTokens = (state: RootState): JWTTokens | null => state.auth.tokens;
export const selectAuthLoading = (state: RootState): boolean => state.auth.isLoading;
export const selectAuthError = (state: RootState): string | null => state.auth.error;
export const selectCurrentStep = (state: RootState): AuthStep => state.auth.currentStep;
export const selectSignUpEmail = (state: RootState): string | null => state.auth.signUpEmail;

export const {
  setUser,
  setTokens,
  setCurrentStep,
  setSignUpEmail,
  clearError,
  resetAuthState
} = authSlice.actions;

export default authSlice.reducer;
