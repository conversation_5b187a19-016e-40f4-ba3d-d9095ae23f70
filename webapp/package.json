{"name": "wefi-b2b-dashboard", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "biome lint .", "format": "biome format . --write", "check": "biome check --apply .", "preview": "vite preview", "test": "vitest", "test:coverage": "vitest run --coverage"}, "dependencies": {"@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-tooltip": "^1.2.7", "@reduxjs/toolkit": "^2.2.1", "@tailwindcss/vite": "^4.1.7", "@tanstack/react-form": "^1.11.2", "@tanstack/react-query": "^5.76.1", "@tanstack/react-router": "^1.120.5", "@tanstack/react-table": "^8.21.3", "@types/styled-components": "^5.1.34", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "lucide-react": "^0.511.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-phone-number-input": "^3.4.12", "react-redux": "^9.1.0", "react-router-dom": "^6.22.1", "recharts": "^2.15.3", "styled-components": "^6.1.18", "tailwind-merge": "^3.3.0", "tailwindcss": "^4.1.7", "tw-animate-css": "^1.3.0", "zod": "^3.23.8"}, "devDependencies": {"@biomejs/biome": "1.5.3", "@tanstack/react-router-devtools": "^1.120.6", "@tanstack/router-plugin": "^1.120.5", "@testing-library/jest-dom": "^6.4.2", "@testing-library/react": "^14.2.1", "@testing-library/user-event": "^14.6.1", "@types/node": "^22.15.21", "@types/react": "^18.2.55", "@types/react-dom": "^18.2.19", "@vitejs/plugin-react": "^4.2.1", "@vitest/coverage-v8": "^1.6.1", "jsdom": "^24.0.0", "typescript": "^5.2.2", "vite": "^5.1.0", "vitest": "^1.2.2"}}