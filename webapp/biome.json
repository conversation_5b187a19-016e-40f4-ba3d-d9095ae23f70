{"$schema": "https://biomejs.dev/schemas/1.5.3/schema.json", "organizeImports": {"enabled": true}, "linter": {"enabled": true, "rules": {"recommended": true, "correctness": {"noUnusedVariables": "error"}, "suspicious": {"noExplicitAny": "error"}, "style": {"useConst": "error", "useTemplate": "error"}}}, "formatter": {"enabled": true, "formatWithErrors": false, "indentStyle": "space", "indentWidth": 2, "lineWidth": 100}, "javascript": {"formatter": {"quoteStyle": "single", "trailingComma": "es5", "semicolons": "always"}}, "files": {"ignore": ["coverage/**", "node_modules/**", "dist/**", "src/routeTree.gen.ts", "*.test.tsx"]}}