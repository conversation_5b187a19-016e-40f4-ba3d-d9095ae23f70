# WeFi B2B Dashboard

This repository is containing two main components that work together to deliver the WeFi B2B Dashboard application:

## Repository Structure

### Webapp (`/webapp`)
The frontend web application. This component handles the user interface and client-side logic of the B2B dashboard.

### BFF (`/bff`)
The Backend-For-Frontend service that acts as a dedicated API layer between the web application and various backend services.

## Getting Started

Each component has its own setup instructions and documentation in their respective directories:

- For the web application, see [webapp/README.md](webapp/README.md)
- For the BFF service, see [bff/README.md](bff/README.md)

## Development

To run the complete application locally, you'll need to:

1. Set up and run the BFF service
2. Set up and run the webapp

Run docker-compose.yml to run additional services (postgres eg.)

```bash
docker-compose up -d
```

Please refer to the individual component README files for detailed setup instructions.